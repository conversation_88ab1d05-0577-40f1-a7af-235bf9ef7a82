/* {{ AURA-X: Add - 关键CSS，优化首屏渲染性能. Source: context7-mcp on 'WP Rocket Performance' }} */

/* 关键CSS - 首屏渲染优化 */
:root {
  --primary-blue: #1A365D;
  --primary-orange: #FF6A00;
  --text-primary: #1a2a3a;
  --bg-primary: #f4f6fa;
  --bg-white: #ffffff;
  /* {{ AURA-X: Modify - 英文化字体设置. Confirmed via 寸止 }} */
  --font-family: 'SF Pro Display', 'Segoe UI', 'Inter', 'Roboto', Arial, sans-serif;
  --font-size-base: 17px;
  --line-height-base: 1.7;
}

/* 基础重置和字体优化 */
* {
  box-sizing: border-box;
}

/* {{ AURA-X: Modify - 简化body样式. Confirmed via 寸止 }} */
body {
  margin: 0;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  padding-top: var(--navbar-height, 75px);
}

/* 固定导航栏（始终生效） */
.main-navbar {
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 100%;
  background: #ffffff;
  min-height: 75px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
}

/* JS 兜底固定定位 */
.main-navbar.is-fixed { position: fixed; top: 0; left: 0; right: 0; }

/* 滚动态样式（由 JS 添加 .scrolled） */
.main-navbar.scrolled {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  backdrop-filter: saturate(1.2) blur(4px);
}

/* 为 fixed 导航预留顶部空间 */
body.has-fixed-navbar { padding-top: var(--navbar-height, 75px); }

/* 与 WordPress 管理工具条兼容（登录状态） */
.admin-bar .main-navbar { top: 32px; }
@media (max-width: 782px) { /* WP 默认断点 */
  .admin-bar .main-navbar { top: 46px; }
}

/* 跳转链接 - 无障碍访问 */
.skip-link {
  position: absolute;
  left: -9999px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.skip-link:focus {
  position: absolute;
  left: 10px;
  top: 10px;
  width: auto;
  height: auto;
  padding: 8px 12px;
  background: var(--primary-blue);
  color: #fff;
  border-radius: 6px;
  z-index: 10000;
}

/* 移除冲突的导航栏样式 - 使用 .main-navbar */

/* 主要内容区域 */
.hero-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 100%);
  color: white;
  text-align: center;
  padding: 2rem 0;
}

.hero-content h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.hero-content p {
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

/* 容器系统 */
.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(1rem, 5vw, 3rem);
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.btn-primary {
  background: var(--primary-orange);
  color: white;
}

.btn-primary:hover {
  background: #E55A00;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  body { padding-top: var(--navbar-height, 75px); }

  .main-navbar {
    min-height: 60px;
    padding: 0 15px;
  }

  .hero-section {
    min-height: 50vh;
    padding: 1.5rem 0;
  }

  .container {
    width: 95%;
    padding: 0 15px;
  }
}

/* 图片优化 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

img[data-src] {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

img.loaded {
  background: none;
  animation: none;
}

/* 内容可见性优化 */
[data-wpr-lazyrender] {
  content-visibility: auto;
}

/* 字体显示优化 */
@font-face {
  font-family: 'SF Pro Display';
  font-display: swap;
}

/* 预加载关键资源 */
.preload-critical {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}
