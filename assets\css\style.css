/* CSS变量系统 - 统一设计令牌 */
:root {
  /* 主色调 */
  --primary-blue: #1A365D;
  --primary-blue-light: #2E5C8A;
  --primary-blue-lighter: #4A90E2;
  --primary-orange: #FF6A00;
  --primary-orange-dark: #E55A00;
  --primary-orange-light: #FF8533;

  /* 中性色 */
  --text-primary: #1a2a3a;
  --text-secondary: #666;
  --text-light: rgba(255,255,255,0.8);
  --bg-primary: #f4f6fa;
  --bg-white: #ffffff;
  --bg-light: #f8f9fa;
  --border-light: #e9ecef;

  /* {{ AURA-X: Modify - 英文化字体设置. Confirmed via 寸止 }} */
  /* Fonts - English optimized */
  --font-family: 'SF Pro Display', 'Segoe UI', 'Inter', 'Roboto', Arial, sans-serif;
  --font-size-base: 17px;
  --font-size-small: 0.85rem;
  --font-size-large: 1.2rem;
  --line-height-base: 1.7;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 15px;
  --spacing-lg: 25px;
  --spacing-xl: 40px;
  --spacing-xxl: 60px;

  /* 圆角 */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 25px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 15px rgba(0,0,0,0.15);
  --shadow-lg: 0 8px 25px rgba(0,0,0,0.2);

  /* 过渡 */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* 渐变样式 - 统一管理所有渐变 */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
  --gradient-primary-full: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 50%, var(--primary-blue-lighter) 100%);
  --gradient-orange: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
  --gradient-orange-dark: linear-gradient(135deg, var(--primary-orange-dark) 0%, var(--primary-orange) 100%);
  --gradient-bg-light: linear-gradient(135deg, var(--bg-light) 0%, var(--border-light) 100%);
  --gradient-bg-primary: linear-gradient(135deg, var(--bg-primary) 0%, var(--border-light) 100%);

  /* 行业特色渐变 */
  --gradient-energy: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  --gradient-automotive: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
  --gradient-packaging: linear-gradient(135deg, #9C27B0 0%, #E1BEE7 100%);
  --gradient-fitness: linear-gradient(135deg, #E91E63 0%, #F06292 100%);
  --gradient-medical: linear-gradient(135deg, #2196F3 0%, #64B5F6 100%);
  --gradient-food: linear-gradient(135deg, #4CAF50 0%, #81C784 100%);
  --gradient-manufacturing: linear-gradient(135deg, #FF6A00 0%, #FF8A50 100%);
  --gradient-construction: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
  --gradient-textile: linear-gradient(135deg, #E91E63 0%, #F06292 100%);
  --gradient-ceramic: linear-gradient(135deg, #D84315 0%, #FF5722 100%);

  /* 响应式断点 */
  --mobile-breakpoint: 480px;
  --tablet-breakpoint: 768px;
  --desktop-breakpoint: 1024px;

  /* 现代化布局系统 */
  --grid-gap: clamp(1rem, 3vw, 2rem);
  --container-padding: clamp(1rem, 5vw, 3rem);
  --section-spacing: clamp(3rem, 8vw, 6rem);

  /* 动画和过渡 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-circ: cubic-bezier(0.85, 0, 0.15, 1);
  --animation-fast: 0.2s var(--ease-out-quart);
  --animation-normal: 0.3s var(--ease-out-quart);
  --animation-slow: 0.6s var(--ease-in-out-circ);

  /* 现代化阴影系统 */
  --shadow-subtle: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-moderate: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-strong: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
  --shadow-intense: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);

  /* 现代化布局系统 */
  --grid-gap: clamp(1rem, 3vw, 2rem);
  --container-padding: clamp(1rem, 5vw, 3rem);
  --section-spacing: clamp(3rem, 8vw, 6rem);

  /* 动画和过渡 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-in-out-circ: cubic-bezier(0.85, 0, 0.15, 1);
  --animation-fast: 0.2s var(--ease-out-quart);
  --animation-normal: 0.3s var(--ease-out-quart);
  --animation-slow: 0.6s var(--ease-in-out-circ);

  /* 现代化阴影系统 */
  --shadow-subtle: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-moderate: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-strong: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
  --shadow-intense: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation: none !important;
    transition: none !important;
    scroll-behavior: auto !important;
  }
}

/* 字体优化 - 预加载和显示优化 */
@font-face {
  font-family: 'SF Pro Display';
  font-display: swap; /* 优化字体加载性能 */
  src: local('SF Pro Display');
}

/* {{ AURA-X: Modify - 强化body样式，确保导航栏空间. Source: context7-mcp on 'CSS sticky navigation' }} */
body {
  margin: 0;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background: var(--bg-primary);

  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-display: swap; /* 字体交换策略 */

  /* 滚动性能优化 */
  overflow-x: hidden;
  overflow-y: auto;
  scroll-behavior: smooth;

  /* 预留与导航等高的顶部空间（JS 会在加载后写入 --navbar-height） */
  padding-top: var(--navbar-height, 75px);

  /* 现代化性能优化 */
  contain: layout style paint;
  will-change: scroll-position;
}

/* 为 fixed 导航预留顶部空间（由 JS 注入高度变量） */
body.has-fixed-navbar { padding-top: var(--navbar-height, 75px); }

/* 主要内容区域确保不被导航栏遮挡 */
main, .main-content, .page-content, .post-content {
  margin-top: 0;
  padding-top: 0;
}

/* Skip link */
.skip-link {
  position: absolute;
  left: -9999px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.skip-link:focus {
  position: absolute;
  left: 10px;
  top: 10px;
  width: auto;
  height: auto;
  padding: 8px 12px;
  background: #1A365D;
  color: #fff;
  border-radius: 6px;
  z-index: 10000;
}

/* Generic content typography */
.page-article .entry-content,
.single-article .entry-content {
  font-size: 1rem;
  line-height: 1.8;
  color: var(--text-primary);
}
.entry-content h1, .entry-content h2, .entry-content h3,
.entry-content h4, .entry-content h5, .entry-content h6 {
  color: #1A365D;
  line-height: 1.3;
  margin: 1.2em 0 0.6em;
}
.entry-content p { margin: 0 0 1em; }
.entry-content ul, .entry-content ol { margin: 0 0 1em 1.25em; }
.entry-content li { margin: 0.25em 0; }
.entry-content blockquote {
  margin: 1em 0;
  padding: 0.8em 1em;
  border-left: 4px solid #4A90E2;
  background: #f7fbff;
}
.entry-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}
.entry-content th, .entry-content td {
  border: 1px solid #e5e7eb;
  padding: 0.6em 0.8em;
  text-align: left;
}
.page-header, .single-header { margin: 2rem 0 1rem; }
.page-title, .single-title {
  font-size: clamp(1.6rem, 3vw, 2.2rem);
  margin: 0 0 0.5rem;
}
.single-meta { color: #6b7280; font-size: 0.9rem; }
.single-nav { display: flex; justify-content: space-between; gap: 10px; margin: 2rem 0; }
.single-nav a { color: #1A365D; text-decoration: none; }
.single-nav a:hover { text-decoration: underline; }

/* 404 */
.page-404 { padding: 80px 0; text-align: center; }
.page-404 h1 { font-size: clamp(2rem, 4vw, 2.6rem); margin: 0 0 0.5rem; }
.page-404 p { color: #6b7280; font-size: 1rem; }

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  border-radius: 6px;
  border: 2px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #999 0%, #666 100%);
}

::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* Firefox滚动条样式 */
html {
  scrollbar-width: thin;
  scrollbar-color: #ccc #f1f1f1;
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 确保页面滚动稳定 */
* {
  box-sizing: border-box;
}

/* 防止水平滚动条出现，确保无底部空白 */
html, body {
  overflow-x: hidden;
  max-width: 100%;
  /* 确保sticky定位正常工作 */
  position: relative;
  /* 确保没有底部空白 */
  margin-bottom: 0;
  padding-bottom: 0;
}

/* 现代化容器系统 - CSS Grid + Flexbox */
.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--container-padding);

  /* 现代化容器查询支持 */
  container-type: inline-size;
  container-name: main-container;
}

/* 现代化网格系统 */
.grid {
  display: grid;
  gap: var(--grid-gap);
}

.grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式网格 */
@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

/* 现代化Flexbox工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* 现代化间距系统 */
.p-0 { padding: 0; }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-0 { margin: 0; }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* 现代化文本工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 头部和导航栏 - 使用CSS变量 */
.hero-section {
  background: var(--gradient-primary-full);
  position: relative;
  color: #fff;
  padding: 0 0 50px 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  overflow: hidden;
  min-height: 480px;
  display: flex;
  align-items: center;
}

/* 背景装饰元素 */
.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(74,144,226,0.3) 0%, transparent 40%);
  pointer-events: none;
  z-index: 1;
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-top: 40px;
  width: 90vw;
  max-width: 1400px;
  margin: 0 auto;
  gap: 30px;
  position: relative;
  z-index: 2;
}

.hero-text {
  flex: 1 1 340px;
  min-width: 260px;
  z-index: 2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 主视觉图片无缝融合效果 */
.hero-image {
  flex: 1 1 400px;
  min-width: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 3;
  transition: all 0.6s ease;
}

.hero-image:hover {
  transform: scale(1.05);
}

/* 移除光晕效果，让图片直接融合 */
.hero-image::before {
  display: none;
}

.hero-image img {
  max-width: 450px;
  width: 100%;
  min-width: 200px;
  height: auto;
  display: block;
  margin: 0 auto;
  border: none;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
  position: relative;
  z-index: 5;
  transition: all 0.6s ease;

  /* 使用更强的mask效果实现边缘完全融合 */
  mask:
    radial-gradient(ellipse 120% 100% at center,
      black 30%,
      rgba(0,0,0,0.9) 50%,
      rgba(0,0,0,0.6) 70%,
      rgba(0,0,0,0.3) 85%,
      transparent 100%);
  -webkit-mask:
    radial-gradient(ellipse 120% 100% at center,
      black 30%,
      rgba(0,0,0,0.9) 50%,
      rgba(0,0,0,0.6) 70%,
      rgba(0,0,0,0.3) 85%,
      transparent 100%);

  /* 调整图片使其更好地融入蓝色背景 */
  filter:
    brightness(1.2)
    contrast(1.1)
    saturate(0.7)
    hue-rotate(15deg)
    opacity(0.9);

  /* 使用screen混合模式让图片"发光"融合到背景中 */
  mix-blend-mode: screen;
}

.hero-image:hover img {
  filter:
    brightness(1.3)
    contrast(1.15)
    saturate(0.8)
    hue-rotate(10deg)
    opacity(0.95);

  /* 悬停时边缘稍微清晰一些 */
  mask:
    radial-gradient(ellipse 120% 100% at center,
      black 35%,
      rgba(0,0,0,0.95) 55%,
      rgba(0,0,0,0.7) 75%,
      rgba(0,0,0,0.4) 88%,
      transparent 100%);
  -webkit-mask:
    radial-gradient(ellipse 120% 100% at center,
      black 35%,
      rgba(0,0,0,0.95) 55%,
      rgba(0,0,0,0.7) 75%,
      rgba(0,0,0,0.4) 88%,
      transparent 100%);
}

/* 移除投影效果，实现完全融合 */
.hero-image::after {
  display: none;
}

/* 添加动态装饰元素 */
.hero-section::after {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 200px;
  height: 200px;
  background:
    radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

/* 额外的装饰圆圈 */
.hero-content::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 5%;
  width: 100px;
  height: 100px;
  background:
    radial-gradient(circle, rgba(74,144,226,0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
  animation: float 8s ease-in-out infinite reverse;
  pointer-events: none;
}

.hero-text h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0 0 15px 0;
  letter-spacing: 0.5px;
  position: relative;
  line-height: 1.2;
}

.hero-text h1::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -10px;
  width: 80px;
  height: 4px;
  background: #ff6a00;
  border-radius: 2px;
}

.hero-text .subtitle {
  font-size: 1.1rem;
  letter-spacing: 1px;
  margin: 0;
  opacity: 0.9;
  line-height: 1.4;
}

/* 蓝色区域主视觉图片优化 */
/* 旧的导航栏样式已移除，使用新的 .main-navbar */

/* {{ AURA-X: Modify - 简化导航栏为CSS Sticky方案. Source: context7-mcp on 'CSS sticky navigation' }} */
/* 导航栏样式 - 简洁Sticky版本 */
.main-navbar {
  background: #ffffff;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 75px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);

  /* 直接使用 fixed，避免 sticky 在特定布局中失效 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;

  border-bottom: 1px solid rgba(26,54,93,0.08);
  transition: all 0.3s ease;
}

/* 兼容：保留 .is-fixed（无副作用） */
.main-navbar.is-fixed { position: fixed; top: 0; left: 0; right: 0; }

.main-navbar.scrolled {
  min-height: 65px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  background: rgba(255,255,255,0.95);
}

.main-navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255,106,0,0.3), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

/* 与 WordPress 管理工具条兼容（登录状态） */
.admin-bar .main-navbar { top: 32px; }
@media (max-width: 782px) {
  .admin-bar .main-navbar { top: 46px; }
}

@keyframes shimmer {
  0%, 100% {
    opacity: 0.3;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100%);
  }
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 40px;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.navbar-logo {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1A365D;
  margin-right: 30px;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.navbar-logo:hover,
.navbar-logo:hover .logo-link {
  color: #FF6A00;
  transform: scale(1.02);
}

.navbar-logo .logo-link:visited {
  color: inherit;
}

.nav-dropdown {
  position: relative;
  display: inline-block;
}

.nav-link {
  color: #1A365D;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 12px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: block;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,106,0,0.1), transparent);
  transition: left 0.5s ease;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  background: var(--gradient-orange);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255,106,0,0.3);
}

.nav-submenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafb 100%);
  min-width: 220px;
  box-shadow:
    0 20px 40px rgba(0,0,0,0.15),
    0 8px 16px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.9);
  border-radius: 12px;
  padding: 12px 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-15px) scale(0.95);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 10000;
  border: 1px solid rgba(26,54,93,0.1);
}

.nav-dropdown:hover .nav-submenu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.nav-submenu a {
  display: block;
  padding: 12px 24px;
  color: #1A365D;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  border-left: 3px solid transparent;
}

.nav-submenu a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, #FF6A00, #FF8533);
  transition: width 0.3s ease;
}

.nav-submenu a:hover {
  background: linear-gradient(90deg, rgba(255,106,0,0.1) 0%, transparent 100%);
  color: #FF6A00;
  transform: translateX(8px);
  border-left-color: #FF6A00;
}

.nav-submenu a:hover::before {
  width: 3px;
}

/* 行业菜单特殊样式 - 美化版 */
.nav-industries .nav-submenu {
  min-width: 720px;
  max-width: 720px; /* 固定宽度防止抖动 */
  width: 720px; /* 强制固定宽度 */
  left: -250px;
  padding: 20px 0;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid rgba(74,144,226,0.1);
  box-shadow:
    0 20px 40px rgba(0,0,0,0.15),
    0 8px 16px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.9);
  overflow: hidden; /* 防止内容溢出导致抖动 */
}

.submenu-header {
  padding: 12px 30px 16px;
  font-size: 0.9rem;
  font-weight: 700;
  color: #ff6a00;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 2px solid #ff6a00;
  margin-bottom: 20px;
  background: linear-gradient(90deg, rgba(255,106,0,0.05) 0%, transparent 100%);
  position: relative;
}

.submenu-header::after {
  content: '';
  position: absolute;
  left: 30px;
  bottom: -2px;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #ff6a00, #e55a00);
  border-radius: 1px;
}

.industries-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0;
  padding: 0 15px;
  width: 100%; /* 确保网格占满容器 */
  box-sizing: border-box;
}

.industry-col {
  display: flex;
  flex-direction: column;
  padding: 0 15px;
  position: relative;
  width: 100%; /* 确保每列宽度一致 */
  box-sizing: border-box;
  min-height: 300px; /* 设置最小高度确保一致性 */
}

.industry-col:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10px;
  bottom: 10px;
  width: 1px;
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(74,144,226,0.2) 20%,
    rgba(74,144,226,0.3) 50%,
    rgba(74,144,226,0.2) 80%,
    transparent 100%);
  pointer-events: none; /* 防止分隔线影响鼠标事件 */
}

.industry-col a {
  display: block;
  padding: 14px 20px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #2E5C8A;
  transition: all 0.25s ease; /* 缩短动画时间减少抖动 */
  border-radius: 8px;
  margin: 2px 0;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  min-height: 20px; /* 统一最小高度 */
  box-sizing: border-box;
  width: 100%; /* 确保宽度一致 */
  text-decoration: none;
  white-space: nowrap; /* 防止文字换行导致高度不一致 */
  line-height: 1.2; /* 统一行高 */
}

.industry-col a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg, #4A90E2, #7BB3F0);
  transition: width 0.25s ease;
  z-index: 1;
}

.industry-col a::after {
  content: '';
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid currentColor;
  opacity: 0;
  transition: all 0.25s ease;
  z-index: 2;
}

.industry-col a span {
  position: relative;
  z-index: 2;
  display: block;
}

.industry-col a:hover {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1A365D;
  transform: translateX(6px); /* 减少移动距离防止抖动 */
  box-shadow:
    0 6px 16px rgba(74,144,226,0.2),
    0 3px 6px rgba(74,144,226,0.1);
  font-weight: 600;
}

.industry-col a:hover::before {
  width: 4px;
}

.industry-col a:hover::after {
  opacity: 0.6;
  transform: translateY(-50%) rotate(90deg);
}

/* 添加行业图标 */
.industry-col a[href*="aluminum"]::before { background: linear-gradient(90deg, #9E9E9E, #BDBDBD); }
.industry-col a[href*="automotive"]::before { background: linear-gradient(90deg, #FF5722, #FF8A65); }
.industry-col a[href*="ceramic"]::before { background: linear-gradient(90deg, #8BC34A, #AED581); }
.industry-col a[href*="appliance"]::before { background: linear-gradient(90deg, #2196F3, #64B5F6); }
.industry-col a[href*="construction"]::before { background: linear-gradient(90deg, #FF9800, #FFB74D); }
.industry-col a[href*="elevator"]::before { background: linear-gradient(90deg, #607D8B, #90A4AE); }
.industry-col a[href*="energy"]::before { background: linear-gradient(90deg, #FFEB3B, #FFF176); }
.industry-col a[href*="fitness"]::before { background: linear-gradient(90deg, #E91E63, #F06292); }
.industry-col a[href*="food"]::before { background: linear-gradient(90deg, #4CAF50, #81C784); }
.industry-col a[href*="agricultural"]::before { background: linear-gradient(90deg, #8BC34A, #AED581); }
.industry-col a[href*="machine"]::before { background: linear-gradient(90deg, #795548, #A1887F); }
.industry-col a[href*="material"]::before { background: linear-gradient(90deg, #FF5722, #FF8A65); }
.industry-col a[href*="packaging"]::before { background: linear-gradient(90deg, #9C27B0, #BA68C8); }
.industry-col a[href*="manufacturing"]::before { background: linear-gradient(90deg, #3F51B5, #7986CB); }
.industry-col a[href*="medical"]::before { background: linear-gradient(90deg, #F44336, #EF5350); }
.industry-col a[href*="automation"]::before { background: linear-gradient(90deg, #00BCD4, #4DD0E1); }
.industry-col a[href*="textile"]::before { background: linear-gradient(90deg, #E91E63, #F06292); }
.industry-col a[href*="tobacco"]::before { background: linear-gradient(90deg, #795548, #A1887F); }

.btn {
  display: inline-block;
  padding: 12px 28px;
  border-radius: 8px;
  font-weight: bold;
  font-size: 1rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
}

/* 统一按钮样式 - 使用CSS变量 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
  color: #fff !important;
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-lg);
  padding: 12px 28px;
  font-size: 1rem;
  font-weight: 600;
  margin-left: 12px;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal) cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.6s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-orange-dark) 0%, var(--primary-orange) 100%);
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-lg);
}

/* 信息区块 */
.info-section {
  background: #fff;
  padding: 48px 0 32px 0;
}
.info-grid {
  display: flex;
  gap: 48px;
  flex-wrap: wrap;
}
.info-block {
  flex: 1 1 320px;
  min-width: 260px;
}
.info-block h2 {
  color: #15304b;
  font-size: 1.4rem;
  margin-bottom: 16px;
  letter-spacing: 1px;
}

/* 行业区块 - 使用CSS变量 */
.industries-section {
  background: var(--gradient-bg-light);
  padding: 60px 0;
  position: relative;
  overflow: hidden;
}

.industries-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="1" fill="%23000" opacity="0.02"/><circle cx="10" cy="60" r="1" fill="%23000" opacity="0.02"/><circle cx="90" cy="40" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.industries-section h2 {
  color: #15304b;
  font-size: 2.2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20px;
  letter-spacing: 1.5px;
  position: relative;
  z-index: 2;
}

.industries-section h2::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #ff6a00, #e65c00);
  border-radius: 2px;
}

.industries-section p {
  text-align: center;
  color: #555;
  font-size: 1.1rem;
  margin-bottom: 40px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

.industries-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px 15px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 30px;
  position: relative;
  z-index: 2;
}

@media (max-width: 1200px) {
  .industries-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 30px 20px;
  }
}

@media (max-width: 800px) {
  .industries-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px 15px;
  }
}

.industry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 15px 8px;
  border-radius: 12px;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  position: relative;
  border: 1px solid rgba(255,255,255,0.8);
  box-shadow:
    0 2px 8px rgba(0,0,0,0.08),
    0 1px 3px rgba(0,0,0,0.05),
    inset 0 1px 0 rgba(255,255,255,0.9);
  overflow: hidden;
}

.industry-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(74,144,226,0.05) 0%, rgba(46,92,138,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.industry-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0,0,0,0.15),
    0 8px 16px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.9);
}

.industry-item:hover::before {
  opacity: 1;
}

.industry-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  box-shadow:
    0 2px 8px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.8);
}

.industry-icon::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: linear-gradient(135deg, rgba(74,144,226,0.1) 0%, rgba(123,179,240,0.1) 100%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.industry-item:hover .industry-icon {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  transform: scale(1.1) rotate(5deg);
  box-shadow:
    0 8px 20px rgba(74,144,226,0.2),
    inset 0 1px 0 rgba(255,255,255,0.9);
}

.industry-item:hover .industry-icon::before {
  opacity: 1;
}

.industry-icon img {
  width: 36px;
  height: 36px;
  object-fit: contain;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.industry-item:hover .industry-icon img {
  transform: scale(1.1);
  filter: brightness(1.1) saturate(1.2);
}

.industry-name {
  font-size: 0.7rem;
  font-weight: 700;
  color: #15304b;
  letter-spacing: 0.5px;
  line-height: 1.2;
  text-transform: uppercase;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.industry-item:hover .industry-name {
  color: #1a365d;
  transform: translateY(-2px);
}



/* 行业项目加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.industry-item {
  animation: fadeInUp 0.6s ease-out forwards;
}

.industry-item:nth-child(1) { animation-delay: 0.1s; }
.industry-item:nth-child(2) { animation-delay: 0.15s; }
.industry-item:nth-child(3) { animation-delay: 0.2s; }
.industry-item:nth-child(4) { animation-delay: 0.25s; }
.industry-item:nth-child(5) { animation-delay: 0.3s; }
.industry-item:nth-child(6) { animation-delay: 0.35s; }
.industry-item:nth-child(7) { animation-delay: 0.4s; }
.industry-item:nth-child(8) { animation-delay: 0.45s; }
.industry-item:nth-child(9) { animation-delay: 0.5s; }
.industry-item:nth-child(10) { animation-delay: 0.55s; }
.industry-item:nth-child(11) { animation-delay: 0.6s; }
.industry-item:nth-child(12) { animation-delay: 0.65s; }
.industry-item:nth-child(13) { animation-delay: 0.7s; }
.industry-item:nth-child(14) { animation-delay: 0.75s; }
.industry-item:nth-child(15) { animation-delay: 0.8s; }
.industry-item:nth-child(16) { animation-delay: 0.85s; }
.industry-item:nth-child(17) { animation-delay: 0.9s; }
.industry-item:nth-child(18) { animation-delay: 0.95s; }

/* 市场区块 */
.markets-section {
  background: #e9edf2;
  padding: 40px 0 48px 0;
}
.markets-section h2 {
  color: #15304b;
  font-size: 1.4rem;
  margin-bottom: 16px;
  letter-spacing: 1px;
}

/* {{ AURA-X: Modify - 简化移动端导航栏. Source: context7-mcp on 'CSS sticky navigation' }} */
@media (max-width: 900px) {
  body {
    padding-top: 0;
  }

  .main-navbar {
    padding: 0 20px;
    min-height: 65px;
    flex-wrap: nowrap;
  }

  .navbar-left {
    gap: 25px;
  }

  .navbar-logo {
    font-size: 1.1rem;
  }

  .navbar-logo .logo-text {
    display: none;
  }

  .logo-icon {
    font-size: 2rem;
  }

  /* 移动端下拉菜单优化 */
  .nav-industries .nav-submenu {
    min-width: 400px;
    max-width: 400px; /* 固定宽度 */
    width: 400px; /* 强制固定宽度 */
    left: -180px;
    padding: 15px 0;
  }

  .industries-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 0 10px;
    width: 100%;
  }

  .industry-col {
    padding: 0 10px;
    min-height: 250px; /* 调整移动端高度 */
  }

  .industry-col a {
    padding: 12px 15px;
    font-size: 0.9rem;
    min-height: 18px;
  }

  .industry-col a:hover {
    transform: translateX(4px); /* 减少移动端移动距离 */
  }

  .submenu-header {
    padding: 10px 20px 12px;
    font-size: 0.85rem;
  }

  .nav-submenu {
    min-width: 180px;
  }

  .nav-link, .nav-submenu a {
    font-size: 0.9rem;
    padding: 8px 12px;
  }
  .hero-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 25px;
    width: 98vw;
    padding-top: 25px;
  }
  .hero-image:hover {
    transform: scale(1.03);
  }
  .hero-image img {
    max-width: 280px;
    min-width: 140px;

    /* 移动设备上的融合效果 */
    mask:
      radial-gradient(ellipse 110% 90% at center,
        black 35%,
        rgba(0,0,0,0.8) 60%,
        rgba(0,0,0,0.4) 80%,
        transparent 100%);
    -webkit-mask:
      radial-gradient(ellipse 110% 90% at center,
        black 35%,
        rgba(0,0,0,0.8) 60%,
        rgba(0,0,0,0.4) 80%,
        transparent 100%);
  }

  /* 行业区块响应式 */
  .industries-section {
    padding: 50px 0;
  }
  .industries-section h2 {
    font-size: 1.9rem;
  }
  .industries-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 18px 12px;
    padding: 0 20px;
  }
  .industry-item {
    padding: 15px 8px;
  }
  .industry-icon {
    width: 55px;
    height: 55px;
    margin-bottom: 10px;
  }
  .industry-icon img {
    width: 32px;
    height: 32px;
  }
  .industry-name {
    font-size: 0.65rem;
  }
}
/* {{ AURA-X: Modify - 简化小屏幕导航栏. Source: context7-mcp on 'CSS sticky navigation' }} */
@media (max-width: 600px) {
  body {
    padding-top: 0;
  }

  .main-navbar {
    padding: 0 2px;
    gap: 4px;
    min-height: 40px;
    flex-wrap: nowrap;
  }
  .main-navbar a, .btn-primary {
    font-size: 0.95rem;
    padding: 6px 6px;
  }

  /* 小屏幕下拉菜单优化 */
  .nav-industries .nav-submenu {
    min-width: 300px;
    max-width: 300px; /* 固定宽度 */
    width: 300px; /* 强制固定宽度 */
    left: -120px;
    padding: 12px 0;
  }

  .industries-grid {
    grid-template-columns: 1fr;
    padding: 0 8px;
    width: 100%;
  }

  .industry-col {
    padding: 0 8px;
    min-height: auto; /* 小屏幕不限制高度 */
  }

  .industry-col:not(:last-child)::after {
    display: none;
  }

  .industry-col a {
    padding: 10px 15px;
    font-size: 0.85rem;
    margin: 1px 0;
    min-height: 16px;
  }

  .industry-col a:hover {
    transform: translateX(3px); /* 进一步减少移动距离 */
  }

  .submenu-header {
    padding: 8px 16px 10px;
    font-size: 0.8rem;
    margin-bottom: 15px;
  }

  .nav-link, .nav-submenu a {
    font-size: 0.85rem;
    padding: 10px 15px;
  }

  .nav-submenu {
    min-width: 160px;
  }
  .hero-text h1 {
    font-size: 1.8rem;
  }
  .container {
    width: 98%;
    padding: 0 4px;
  }
  .hero-image:hover {
    transform: scale(1.02);
  }
  .hero-image img {
    max-width: 200px;
    width: 90%;
    min-width: 100px;

    /* 小屏幕上的融合效果 */
    mask:
      radial-gradient(ellipse 100% 80% at center,
        black 40%,
        rgba(0,0,0,0.7) 65%,
        rgba(0,0,0,0.3) 85%,
        transparent 100%);
    -webkit-mask:
      radial-gradient(ellipse 100% 80% at center,
        black 40%,
        rgba(0,0,0,0.7) 65%,
        rgba(0,0,0,0.3) 85%,
        transparent 100%);
  }

  /* 小屏幕行业区块 */
  .industries-section {
    padding: 40px 0;
  }
  .industries-section h2 {
    font-size: 1.7rem;
    margin-bottom: 15px;
  }
  .industries-section p {
    font-size: 0.95rem;
    margin-bottom: 30px;
    padding: 0 15px;
  }
  .industries-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px 8px;
    padding: 0 15px;
  }
  .industry-item {
    padding: 12px 6px;
  }
  .industry-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 8px;
  }
  .industry-icon img {
    width: 28px;
    height: 28px;
  }
  .industry-name {
    font-size: 0.6rem;
    letter-spacing: 0.3px;
  }
}

/* 超小屏幕行业区块 */
@media (max-width: 480px) {
  .industries-section {
    padding: 35px 0;
  }
  .industries-section h2 {
    font-size: 1.5rem;
    margin-bottom: 12px;
  }
  .industries-section p {
    font-size: 0.9rem;
    margin-bottom: 25px;
    padding: 0 10px;
  }
  .industries-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px 6px;
    padding: 0 10px;
  }
  .industry-item {
    padding: 10px 4px;
  }
  .industry-icon {
    width: 45px;
    height: 45px;
    margin-bottom: 6px;
  }
  .industry-icon img {
    width: 24px;
    height: 24px;
  }
  .industry-name {
    font-size: 0.55rem;
    letter-spacing: 0.2px;
  }
}

/* 行业页面样式 */
.industry-page {
  background: #f8f9fa;
}

.industry-hero {
  background: var(--gradient-primary-full);
  color: white;
  padding: 80px 0 60px;
  position: relative;
  overflow: hidden;
}

.industry-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.industry-hero .container {
  display: flex;
  align-items: center;
  gap: 60px;
  position: relative;
  z-index: 2;
}

.industry-hero-content {
  flex: 1;
}

.industry-breadcrumb {
  font-size: 0.9rem;
  margin-bottom: 20px;
  opacity: 0.8;
}

.industry-breadcrumb a {
  color: white;
  text-decoration: none;
}

.industry-breadcrumb a:hover {
  text-decoration: underline;
}

.industry-breadcrumb span {
  margin: 0 8px;
}

.industry-breadcrumb .current {
  font-weight: 600;
}

.industry-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.industry-subtitle {
  font-size: 1.3rem;
  opacity: 0.9;
  line-height: 1.6;
}

.industry-hero-image {
  flex: 0 0 400px;
}

.industry-hero-image img {
  width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

/* 行业概述部分 */
.industry-overview {
  padding: 80px 0;
  background: white;
}

.industry-overview h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 30px;
  text-align: center;
}

.overview-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.overview-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
}

.overview-text p {
  margin-bottom: 20px;
}

/* 产品推荐部分 */
.industry-products {
  padding: 80px 0;
  background: #f8f9fa;
}

.industry-products h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  padding: 25px;
}

.product-info h3 {
  font-size: 1.3rem;
  color: #1A365D;
  margin-bottom: 15px;
}

.product-info p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.btn-outline {
  background: transparent;
  border: 2px solid #4A90E2;
  color: #4A90E2;
  padding: 10px 20px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-outline:hover {
  background: #4A90E2;
  color: white;
}

/* 应用场景部分 */
.industry-applications {
  padding: 80px 0;
  background: white;
}

.industry-applications h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.applications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.application-item {
  text-align: center;
  padding: 30px 20px;
  border-radius: 12px;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.application-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  background: white;
}

.application-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.application-icon img,
.application-icon svg {
  width: 48px;
  height: 48px;
}

.application-item h3 {
  font-size: 1.2rem;
  color: #1A365D;
  margin-bottom: 15px;
}

.application-item p {
  color: #666;
  line-height: 1.6;
}

/* CTA部分 */
.industry-cta {
  padding: 80px 0;
  background: var(--gradient-primary);
  color: white;
  text-align: center;
}

/* ========================================
   统一移动端响应式样式 (480px及以下)
   合并所有重复的媒体查询以减少代码冗余
   ======================================== */
@media (max-width: 480px) {
  /* 通用section padding统一 */
  .energy-sectors, .application-showcase, .energy-specs,
  .packaging-applications, .packaging-integration,
  .automotive-sectors, .manufacturing-process, .automotive-success,
  .fitness-categories, .fitness-applications, .fitness-success,
  .manufacturing-sectors, .manufacturing-integration, .manufacturing-success,
  .ceramic-glass-challenges,
  .food-challenges,
  .medical-sectors, .medical-certifications,
  .appliance-categories, .appliance-trends, .appliance-success,
  .agricultural-sectors, .seasonal-requirements, .agricultural-success,
  .automation-sectors, .industry-4-technologies, .automation-success,
  .construction-sectors, .safety-standards, .construction-success,
  .machine-tools-categories, .precision-requirements, .machine-tools-success,
  .textile-sectors, .textile-process, .textile-success,
  .elevator-systems, .elevator-safety-standards, .elevator-success,
  .material-handling-systems, .material-flow-process, .material-handling-success,
  .tobacco-processing-systems, .tobacco-production-process, .tobacco-success,
  .core-advantages, .certifications-showcase {
    padding: 60px 0;
  }

  /* 网格布局统一为单列 */
  .sectors-grid, .applications-grid, .stories-grid, .showcase-grid,
  .challenges-grid, .solutions-grid, .process-flow,
  .success-stories .success-stories {
    gap: 20px;
  }

  /* 卡片padding统一 */
  .sector-card, .application-card, .story-card, .showcase-item,
  .challenge-card, .solution-card, .timeline-step, .process-stage,
  .property-card, .case-study {
    padding: 20px;
  }

  /* Hero features统一布局 */
  .energy-industry .hero-features,
  .packaging-industry .hero-features,
  .automotive-industry .hero-features,
  .fitness-industry .hero-features,
  .manufacturing-industry .hero-features,
  .ceramic-glass-industry .hero-features,
  .food-industry .hero-features,
  .medical-industry .hero-features,
  .appliance-industry .hero-features,
  .agricultural-industry .hero-features,
  .automation-industry .hero-features,
  .construction-industry .hero-features,
  .machine-tools-industry .hero-features,
  .textile-industry .hero-features,
  .elevator-industry .hero-features,
  .material-handling-industry .hero-features,
  .tobacco-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  /* Feature item统一样式 */
  .energy-industry .feature-item,
  .packaging-industry .feature-item,
  .automotive-industry .feature-item,
  .fitness-industry .feature-item,
  .manufacturing-industry .feature-item,
  .ceramic-glass-industry .feature-item,
  .food-industry .feature-item,
  .medical-industry .feature-item,
  .appliance-industry .feature-item,
  .agricultural-industry .feature-item,
  .automation-industry .feature-item,
  .construction-industry .feature-item,
  .machine-tools-industry .feature-item,
  .textile-industry .feature-item,
  .elevator-industry .feature-item,
  .material-handling-industry .feature-item,
  .tobacco-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  /* 成功案例网格统一 */
  .energy-industry .challenges-grid,
  .energy-industry .solutions-grid,
  .packaging-industry .challenges-grid,
  .packaging-industry .solutions-grid,
  .automotive-industry .solutions-grid,
  .fitness-industry .solutions-grid,
  .manufacturing-industry .solutions-grid,
  .ceramic-glass-industry .solutions-grid,
  .food-industry .solutions-grid,
  .medical-industry .solutions-grid,
  .appliance-industry .solutions-grid,
  .agricultural-industry .solutions-grid,
  .automation-industry .solutions-grid,
  .construction-industry .solutions-grid,
  .machine-tools-industry .solutions-grid,
  .textile-industry .solutions-grid,
  .elevator-industry .solutions-grid,
  .material-handling-industry .solutions-grid,
  .tobacco-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  /* 指标项统一样式 */
  .result-item, .metric-item {
    min-width: 80px;
  }

  .result-number, .metric-number {
    font-size: 1.5rem;
  }

  /* 产品section优化 */
  .products-section {
    padding: 40px 0;
  }

  /* Hero section移动端优化 */
  .hero-section {
    min-height: 350px;
    padding: 0 0 30px 0;
  }

  .hero-content {
    padding-top: 20px;
    gap: 20px;
  }

  .hero-text h1 {
    font-size: 1.6rem;
  }

  .hero-text .hero-slogan {
    font-size: 1rem;
  }

  /* 统计数据网格 */
  .market-stats, .about-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: 15px;
  }

  .stat-number {
    font-size: 2rem;
  }

  /* 产品分类 */
  .product-category {
    padding: 30px 20px;
  }

  /* 认证展示 */
  .certifications {
    justify-content: center;
  }

  /* 联系信息 */
  .contact-info {
    text-align: left;
  }
}

.cta-content h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.btn-large {
  padding: 15px 40px;
  font-size: 1.1rem;
}

/* 行业页面响应式设计 */
@media (max-width: 900px) {
  .industry-hero .container {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .industry-hero-image {
    flex: none;
    max-width: 300px;
  }

  .industry-title {
    font-size: 2.5rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .applications-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 600px) {
  .industry-hero {
    padding: 60px 0 40px;
  }

  .industry-title {
    font-size: 2rem;
  }

  .industry-subtitle {
    font-size: 1.1rem;
  }

  .industry-overview,
  .industry-products,
  .industry-applications,
  .industry-cta {
    padding: 60px 0;
  }

  .industry-overview h2,
  .industry-products h2,
  .industry-applications h2,
  .cta-content h2 {
    font-size: 2rem;
  }

  .applications-grid {
    grid-template-columns: 1fr;
  }
}

/* 铝材行业页面专用样式 */
.aluminum-industry .hero-features {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  flex-wrap: wrap;
}

/* 铝材行业挑战部分 - 单行布局 */
.aluminum-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.aluminum-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #FF6B35;
}

.aluminum-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.aluminum-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.aluminum-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 铝材行业产品解决方案优化 - 3卡片居中显示 */
.aluminum-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 30px !important;
  max-width: 1000px !important;
  margin: 0 auto !important;
  justify-content: center !important;
}

/* 铝材行业解决方案卡片布局优化 */
.aluminum-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.aluminum-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.aluminum-industry .product-features {
  flex: 1 !important;
}

.aluminum-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

.aluminum-industry .feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255,255,255,0.1);
  padding: 12px 20px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.aluminum-industry .feature-icon {
  font-size: 1.2rem;
}

/* 行业挑战部分 */
.industry-challenges {
  padding: 80px 0;
  background: #f8f9fa;
}

.industry-challenges h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
}

.challenge-item {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  min-height: 320px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.challenge-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.challenge-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.challenge-item h3 {
  font-size: 1.3rem;
  color: #1A365D;
  margin-bottom: 15px;
}

.challenge-item p {
  color: #666;
  line-height: 1.6;
}

/* 产品解决方案部分 */
.product-solutions {
  padding: 80px 0;
  background: white;
}

.product-solutions h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 20px;
  text-align: center;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #666;
  text-align: center;
  margin-bottom: 50px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 25px;
}

/* 健身行业产品解决方案优化 - 单排显示 */
.fitness-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.fitness-industry .solution-category {
  background: #f8f9fa !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.fitness-industry .solution-category:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15) !important;
}

.solution-category {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

/* 通用悬停效果 - 仅适用于非包装行业 */
.solution-category:hover {
  transform: translateY(-5px);
}

/* 包装行业覆盖通用样式 */
.packaging-industry .solution-category:hover {
  transform: translateY(-8px) !important;
}

.category-header {
  background: var(--gradient-primary);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-header h3 {
  font-size: 1.2rem;
  margin: 0;
}

.temp-rating, .chem-rating, .load-rating, .precision-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.category-content {
  padding: 25px;
}

.product-features ul {
  list-style: none;
  padding: 0;
  margin-bottom: 20px;
}

.product-features li {
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
}

.product-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #4A90E2;
  font-weight: bold;
}

.applications h4 {
  color: #1A365D;
  margin-bottom: 10px;
  font-size: 1rem;
}

.applications p {
  color: #666;
  font-style: italic;
}

/* 流程应用部分 */
.process-applications {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.process-applications h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.process-timeline {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
}

.process-step {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  position: relative;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.process-step:hover {
  transform: translateY(-5px);
}

.step-number {
  position: absolute;
  top: -15px;
  left: 30px;
  background: var(--gradient-orange-dark);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
}

.step-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.step-content h3 {
  color: #1A365D;
  margin-bottom: 15px;
  margin-top: 10px;
  font-size: 1.3rem;
}

.step-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.step-specs {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.step-specs span {
  background: #f0f8ff;
  color: #2E5C8A;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 技术规格部分 */
.technical-specs {
  padding: 80px 0;
  background: white;
}

.technical-specs h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.spec-category {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.spec-category h3 {
  background: linear-gradient(135deg, #4A90E2, #7BB3F0);
  color: white;
  padding: 20px;
  margin: 0;
  font-size: 1.2rem;
  text-align: center;
}

.spec-table {
  padding: 25px;
}

.spec-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-label {
  color: #555;
  font-weight: 500;
}

.spec-value {
  color: #1A365D;
  font-weight: 600;
  background: #e3f2fd;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.9rem;
}

/* CTA部分增强 */
.aluminum-industry .cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 30px;
}

.aluminum-industry .btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
  min-width: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .aluminum-industry .hero-features {
    flex-direction: column;
    gap: 15px;
  }

  .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .solutions-grid,
  .specs-grid {
    grid-template-columns: 1fr;
  }

  .process-timeline {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .aluminum-industry .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .aluminum-industry .btn-large {
    min-width: auto;
    width: 100%;
    max-width: 300px;
  }
}

/* 能源行业页面专用样式 */
.energy-industry .hero-features .feature-item {
  background: rgba(76,175,80,0.1);
  border: 1px solid rgba(76,175,80,0.3);
}

/* 能源行业板块 */
.energy-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
}

.energy-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.sectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.sector-card.traditional::before {
  background: linear-gradient(90deg, #FF9800, #FFC107);
}

.sector-card.oil-gas::before {
  background: linear-gradient(90deg, #2196F3, #03A9F4);
}

.sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sector-applications li {
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.sector-applications li::before {
  content: '⚡';
  position: absolute;
  left: 0;
  color: #4CAF50;
}

.sector-card.traditional .sector-applications li::before {
  color: #FF9800;
}

.sector-card.oil-gas .sector-applications li::before {
  color: #2196F3;
}

/* 产品解决方案增强 */
.energy-industry .solution-category.wind .category-header {
  background: var(--gradient-energy);
}

.energy-industry .solution-category.solar .category-header {
  background: linear-gradient(135deg, #FF9800, #FFC107);
}

.energy-industry .solution-category.power-gen .category-header {
  background: linear-gradient(135deg, #9C27B0, #E91E63);
}

.energy-industry .solution-category.oil-gas-belts .category-header {
  background: linear-gradient(135deg, #2196F3, #03A9F4);
}

/* 应用展示 */
.application-showcase {
  padding: 80px 0;
  background: white;
}

.application-showcase h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.showcase-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
}

.showcase-item {
  display: flex;
  gap: 25px;
  background: #f8f9fa;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

.showcase-item:hover {
  transform: translateY(-5px);
}

.showcase-image {
  flex: 0 0 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.image-placeholder.wind-turbine {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
}

.image-placeholder.solar-farm {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
}

.image-placeholder.power-plant {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.showcase-content {
  flex: 1;
}

.showcase-content h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.showcase-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

.showcase-specs {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.showcase-specs span {
  background: #e3f2fd;
  color: #1A365D;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 技术规格标签页 */
.energy-specs {
  background: #f8f9fa;
}

.specs-tabs {
  max-width: 1000px;
  margin: 0 auto;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  gap: 0;
  margin-bottom: 40px;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.tab-btn {
  flex: 1;
  padding: 15px 25px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: linear-gradient(135deg, #4A90E2, #7BB3F0);
  color: white;
  box-shadow: 0 4px 12px rgba(74,144,226,0.3);
}

.tab-btn:hover:not(.active) {
  background: #f0f8ff;
  color: #4A90E2;
}

.tab-content {
  display: none;
  animation: fadeIn 0.3s ease;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 能源CTA增强 */
.energy-cta {
  background: linear-gradient(135deg, #1A365D 0%, #2E5C8A 50%, #4A90E2 100%);
  position: relative;
  overflow: hidden;
}

.energy-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(76,175,80,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,152,0,0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 能源行业响应式设计 */
@media (max-width: 768px) {
  .sectors-grid {
    grid-template-columns: 1fr;
  }

  .sector-card {
    padding: 25px;
  }

  .showcase-grid {
    grid-template-columns: 1fr;
  }

  .showcase-item {
    flex-direction: column;
    text-align: center;
  }

  .showcase-image {
    flex: none;
    margin-bottom: 20px;
  }

  .tab-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .tab-btn {
    padding: 12px 20px;
  }

  .showcase-specs {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .energy-sectors,
  .application-showcase,
  .energy-specs {
    padding: 60px 0;
  }

  .sectors-grid,
  .showcase-grid {
    gap: 20px;
  }

  .sector-card,
  .showcase-item {
    padding: 20px;
  }

  .energy-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .energy-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .energy-industry .energy-challenges {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .energy-industry .stories-grid {
    grid-template-columns: 1fr !important;
    gap: 30px !important;
    max-width: 500px !important;
  }

  .energy-industry .results-metrics {
    grid-template-columns: 1fr !important;
    gap: 10px !important;
  }

  .energy-industry .showcase-item {
    flex-direction: column !important;
    text-align: center !important;
    gap: 20px !important;
  }

  .energy-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }
}

/* 能源行业挑战部分优化 */
.energy-industry .energy-challenges {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 25px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 能源行业成功案例 */
.energy-industry .stories-grid {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 40px !important;
  max-width: 1000px !important;
  margin: 0 auto !important;
  justify-items: center !important;
}

.energy-industry .story-card {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  width: 100% !important;
  max-width: 480px !important;
  position: relative;
}

.energy-industry .story-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.energy-industry .story-title-group {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e8f5e8;
}

.energy-industry .story-title-group h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 8px;
  font-weight: 600;
}

.energy-industry .story-location {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.energy-industry .story-challenge,
.energy-industry .story-solution {
  margin-bottom: 25px;
}

.energy-industry .story-challenge h4 {
  color: #E53E3E;
  font-size: 1rem;
  margin-bottom: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.energy-industry .story-solution h4 {
  color: #38A169;
  font-size: 1rem;
  margin-bottom: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.energy-industry .story-results h4 {
  color: #1A365D;
  font-size: 1rem;
  margin-bottom: 15px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.energy-industry .results-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 15px;
  align-items: stretch;
}

.energy-industry .metric {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 16px 10px;
  background: linear-gradient(135deg, #f8f9fa, #e8f5e8);
  border-radius: 12px;
  border: 1px solid rgba(76,175,80,0.2);
  height: 100px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.energy-industry .metric:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76,175,80,0.15);
}

.energy-industry .metric-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
  margin-bottom: 4px;
  line-height: 1;
}

.energy-industry .metric-label {
  font-size: 0.75rem;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1.1;
  text-align: center;
  max-width: 100%;
}

/* 能源行业应用展示优化 */
.energy-industry .energy-applications {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: 30px !important;
  max-width: 1000px !important;
  margin: 0 auto !important;
}

.energy-industry .showcase-item {
  display: flex !important;
  gap: 25px !important;
  background: #f8f9fa !important;
  border-radius: 16px !important;
  padding: 30px !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
  transition: transform 0.3s ease !important;
  align-items: center !important;
}

.energy-industry .showcase-item:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
}

/* 能源行业产品解决方案优化 */
.energy-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.energy-industry .solution-category {
  background: #f8f9fa !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.energy-industry .solution-category:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15) !important;
}

/* 能源行业解决方案卡片布局优化 */
.energy-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.energy-industry .product-features {
  flex: 1 !important;
}

.energy-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 包装行业页面专用样式 */
.packaging-industry .hero-features .feature-item {
  background: rgba(156,39,176,0.1);
  border: 1px solid rgba(156,39,176,0.3);
}

/* 包装行业挑战卡片优化 - 单排显示 */
.packaging-industry .challenges-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.packaging-industry .challenge-item {
  background: white !important;
  border-radius: 12px !important;
  padding: 25px !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  text-align: center !important;
}

.packaging-industry .challenge-item:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15) !important;
}

/* 包装行业产品解决方案优化 - 渐进式展示 */
.packaging-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 30px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 包装行业解决方案卡片基础样式 */
.packaging-industry .solution-category {
  background: white !important;
  border-radius: 16px !important;
  padding: 0 !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1) !important;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  position: relative !important;
  overflow: hidden !important;
  min-height: 400px !important;
  will-change: transform !important;
  backface-visibility: hidden !important;
}

.packaging-industry .solution-category:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 20px 60px rgba(0,0,0,0.18) !important;
}

/* 包装行业卡片头部样式 */
.packaging-industry .category-header {
  background: linear-gradient(135deg, #9C27B0 0%, #E1BEE7 100%) !important;
  color: white !important;
  padding: 20px !important;
  text-align: center !important;
  position: relative !important;
  min-height: 100px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.packaging-industry .category-header h3 {
  font-size: 1.3rem !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  line-height: 1.2 !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-height: 2.4em !important;
  color: white !important;
}

/* 包装行业评级标签样式 */
.packaging-industry .speed-rating,
.packaging-industry .hygiene-rating,
.packaging-industry .precision-rating,
.packaging-industry .cleanroom-rating {
  background: rgba(255,255,255,0.2) !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  backdrop-filter: blur(10px) !important;
  color: white !important;
}

/* 包装行业卡片内容区域 */
.packaging-industry .category-content {
  padding: 25px !important;
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  position: relative !important;
}

/* 包装行业产品特性 - 渐进式展示 */
.packaging-industry .product-features {
  flex: 1 !important;
  margin-bottom: 20px !important;
  position: relative !important;
  overflow: hidden !important;
  transition: max-height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  max-height: 140px !important;
}

.packaging-industry .product-features ul {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.packaging-industry .product-features li {
  padding: 8px 0 !important;
  padding-left: 20px !important;
  position: relative !important;
  color: #555 !important;
  line-height: 1.4 !important;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  opacity: 1 !important;
  transform: none !important;
}

.packaging-industry .product-features li::before {
  content: '✓' !important;
  position: absolute !important;
  left: 0 !important;
  color: #9C27B0 !important;
  font-weight: bold !important;
  font-size: 1.1em !important;
}

/* 隐藏第4、5个特性 */
.packaging-industry .product-features li:nth-child(n+4) {
  opacity: 0.4 !important;
  font-size: 0.9em !important;
}

/* 展开指示器 */
.packaging-industry .product-features::after {
  content: '···' !important;
  position: absolute !important;
  bottom: 0 !important;
  right: 0 !important;
  background: linear-gradient(90deg, transparent, white 30%) !important;
  padding: 0 15px 0 30px !important;
  color: #9C27B0 !important;
  font-weight: bold !important;
  font-size: 1.2em !important;
  line-height: 1 !important;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.3s ease !important;
  transform: translateY(0) !important;
}

/* 悬停状态：压缩Applications，展示完整特性 */
.packaging-industry .solution-category:hover .applications {
  padding: 10px 25px 15px 25px !important;
  margin: 0 -25px -25px -25px !important;
  background: rgba(156, 39, 176, 0.08) !important;
  border-top: 1px solid rgba(156, 39, 176, 0.2) !important;
}

.packaging-industry .solution-category:hover .applications h4 {
  font-size: 0.9rem !important;
  margin-bottom: 5px !important;
}

.packaging-industry .solution-category:hover .applications p {
  font-size: 0.85rem !important;
  line-height: 1.3 !important;
  -webkit-line-clamp: 2 !important;
  min-height: 2.6em !important;
}

.packaging-industry .solution-category:hover .product-features {
  margin-bottom: 15px !important;
}

.packaging-industry .solution-category:hover .product-features li:nth-child(n+4) {
  opacity: 1 !important;
  font-size: 1em !important;
  transition-delay: 0.2s !important;
}

.packaging-industry .solution-category:hover .product-features::after {
  opacity: 0 !important;
  transform: translateY(5px) !important;
}

/* 包装行业应用场景区域 */
.packaging-industry .applications {
  margin-top: auto !important;
  padding-top: 20px !important;
  background: rgba(156, 39, 176, 0.05) !important;
  margin: 0 -25px -25px -25px !important;
  padding: 20px 25px 25px 25px !important;
  border-radius: 0 0 16px 16px !important;
  border-top: 2px solid #f0f0f0 !important;
}

.packaging-industry .applications h4 {
  color: #9C27B0 !important;
  margin-bottom: 10px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
}

.packaging-industry .applications p {
  color: #666 !important;
  font-style: italic !important;
  line-height: 1.5 !important;
  position: relative !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 3 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-height: 4.5em !important;
}

/* 包装行业特殊颜色主题 */
.packaging-industry .solution-category.high-speed .category-header {
  background: linear-gradient(135deg, #FF5722 0%, #FF8A65 100%) !important;
}

.packaging-industry .solution-category.food-grade .category-header {
  background: linear-gradient(135deg, #4CAF50 0%, #81C784 100%) !important;
}

.packaging-industry .solution-category.precision .category-header {
  background: linear-gradient(135deg, #2196F3 0%, #64B5F6 100%) !important;
}

.packaging-industry .solution-category.cleanroom .category-header {
  background: linear-gradient(135deg, #9C27B0 0%, #BA68C8 100%) !important;
}

/* 包装行业动画性能优化 */
.packaging-industry .solution-category {
  backface-visibility: hidden !important;
  perspective: 1000px !important;
}

.packaging-industry .product-features li {
  backface-visibility: hidden !important;
}

/* 悬停时的微妙视觉增强 */
.packaging-industry .solution-category:hover .category-header {
  transform: translateY(-1px) !important;
  transition: transform 0.3s ease !important;
}

.packaging-industry .solution-category:hover .product-features li::before {
  color: #7B1FA2 !important;
  transition: color 0.3s ease !important;
}

/* 离开悬停状态时的平滑回退 */
.packaging-industry .product-features li:nth-child(n+4) {
  transition-delay: 0s !important;
}

.packaging-industry .solution-category:not(:hover) .product-features li:nth-child(n+4) {
  transition-delay: 0.05s !important;
}

/* 包装应用部分 */
.packaging-applications {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #f3e5f5 100%);
}

.packaging-applications h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.applications-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.application-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.application-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.application-card.pharmaceutical::before {
  background: linear-gradient(90deg, #2196F3, #03A9F4);
}

.application-card.consumer-goods::before {
  background: linear-gradient(90deg, #FF9800, #FFC107);
}

.application-card.industrial::before {
  background: linear-gradient(90deg, #9C27B0, #E91E63);
}

.application-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.application-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.application-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.application-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.application-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.application-features li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.application-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #4CAF50;
  font-weight: bold;
}

.application-card.pharmaceutical .application-features li::before {
  color: #2196F3;
}

.application-card.consumer-goods .application-features li::before {
  color: #FF9800;
}

.application-card.industrial .application-features li::before {
  color: #9C27B0;
}

/* 产品解决方案增强 */
.packaging-industry .solution-category.high-speed .category-header {
  background: linear-gradient(135deg, #E91E63, #F06292);
}

.packaging-industry .solution-category.food-grade .category-header {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
}

.packaging-industry .solution-category.precision .category-header {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.packaging-industry .solution-category.cleanroom .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.speed-rating, .hygiene-rating, .precision-rating, .cleanroom-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 包装线集成 */
.packaging-integration {
  padding: 80px 0;
  background: white;
}

.packaging-integration h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.integration-timeline {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.timeline-step {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  position: relative;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.timeline-step:hover {
  transform: translateY(-5px);
}

.step-number {
  position: absolute;
  top: -15px;
  left: 30px;
  background: linear-gradient(135deg, #9C27B0, #E91E63);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.step-content h3 {
  color: #1A365D;
  margin-bottom: 15px;
  margin-top: 10px;
  font-size: 1.3rem;
}

.step-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.step-specs {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.step-specs span {
  background: #e8eaf6;
  color: #3f51b5;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 成功案例 */
.success-stories {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #f3e5f5 100%);
}

.success-stories h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.story-category {
  background: linear-gradient(135deg, #9C27B0, #E91E63);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.result-item {
  text-align: center;
  flex: 1;
  min-width: 80px;
}

.result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #9C27B0;
  margin-bottom: 5px;
}

.result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 技术规格对比 */
.packaging-specs {
  background: white;
}

.specs-comparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 包装CTA增强 */
.packaging-cta {
  background: linear-gradient(135deg, #9C27B0 0%, #E91E63 50%, #FF6B9D 100%);
  position: relative;
  overflow: hidden;
}

.packaging-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.cta-features {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-feature {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.cta-feature .feature-icon {
  font-size: 1.2rem;
}

/* 包装行业平板端响应式设计 */
@media (max-width: 1024px) and (min-width: 769px) {
  .packaging-industry .challenges-grid,
  .packaging-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }
}

/* 包装行业响应式设计 */
@media (max-width: 1024px) {
  .applications-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .integration-timeline {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  /* 包装行业响应式优化 */
  .packaging-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 25px !important;
  }

  .packaging-industry .solution-category {
    min-height: 350px !important;
  }

  .packaging-industry .product-features {
    max-height: 120px !important;
  }
}

@media (max-width: 768px) {
  .applications-grid,
  .stories-grid {
    grid-template-columns: 1fr;
  }

  .application-card,
  .story-card {
    padding: 25px;
  }

  /* 包装行业移动端优化 */
  .packaging-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .packaging-industry .solution-category {
    min-height: 320px !important;
    cursor: pointer !important;
  }

  .packaging-industry .category-header {
    min-height: 80px !important;
    padding: 15px !important;
  }

  .packaging-industry .category-header h3 {
    font-size: 1.2rem !important;
  }

  .packaging-industry .category-content {
    padding: 20px !important;
  }

  .packaging-industry .product-features {
    max-height: 100px !important;
  }

  .packaging-industry .applications {
    padding: 15px 20px 20px 20px !important;
    margin: 0 -20px -20px -20px !important;
  }

  /* 移动端点击展开效果 */
  .packaging-industry .solution-category:hover .product-features {
    max-height: 100px !important;
  }

  .packaging-industry .solution-category.expanded .product-features {
    max-height: none !important;
  }
}

  .integration-timeline {
    grid-template-columns: 1fr;
  }

  .timeline-step {
    padding: 25px;
  }

  .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .story-results {
    justify-content: space-around;
  }

  .specs-comparison {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .cta-features {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .packaging-applications,
  .packaging-integration,
  .success-stories,
  .packaging-specs {
    padding: 60px 0;
  }

  .applications-grid,
  .stories-grid {
    gap: 20px;
  }

  .packaging-industry .challenges-grid,
  .packaging-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .application-card,
  .story-card,
  .timeline-step {
    padding: 20px;
  }

  .packaging-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .packaging-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .packaging-industry .challenges-grid,
  .packaging-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .result-item {
    min-width: 70px;
  }

  .result-number {
    font-size: 1.5rem;
  }
}

/* 汽车行业页面专用样式 */
.automotive-industry .hero-features .feature-item {
  background: rgba(25,118,210,0.1);
  border: 1px solid rgba(25,118,210,0.3);
}

/* 汽车行业板块 */
.automotive-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.automotive-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.automotive-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.automotive-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.automotive-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1976D2, #42A5F5);
}

.automotive-sectors .sector-card.engine-powertrain::before {
  background: linear-gradient(90deg, #D32F2F, #EF5350);
}

.automotive-sectors .sector-card.tire-production::before {
  background: linear-gradient(90deg, #388E3C, #66BB6A);
}

.automotive-sectors .sector-card.parts-components::before {
  background: linear-gradient(90deg, #F57C00, #FFA726);
}

.automotive-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.automotive-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.automotive-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.automotive-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.automotive-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: auto;
}

.automotive-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.automotive-sectors .sector-applications li::before {
  content: '🔧';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.automotive-sectors .sector-card.engine-powertrain .sector-applications li::before {
  content: '⚙️';
}

.automotive-sectors .sector-card.tire-production .sector-applications li::before {
  content: '🛞';
}

.automotive-sectors .sector-card.parts-components .sector-applications li::before {
  content: '🔩';
}

/* 产品解决方案增强 */
.automotive-industry .solution-category.assembly-line .category-header {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
}

.automotive-industry .solution-category.machining .category-header {
  background: linear-gradient(135deg, #D32F2F, #EF5350);
}

.automotive-industry .solution-category.tire-building .category-header {
  background: linear-gradient(135deg, #388E3C, #66BB6A);
}

.automotive-industry .solution-category.heavy-duty .category-header {
  background: linear-gradient(135deg, #F57C00, #FFA726);
}

/* 确保汽车行业的 heavy-duty 卡片没有左边框 */
.automotive-industry .solution-category.heavy-duty {
  border-left: none !important;
}

/* 汽车行业产品解决方案优化 - 单排显示 */
.automotive-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.automotive-industry .solution-category {
  background: #f8f9fa !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.automotive-industry .solution-category:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15) !important;
}

/* 汽车行业解决方案卡片布局优化 */
.automotive-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.automotive-industry .product-features {
  flex: 1 !important;
  margin-bottom: 15px !important;
}

.automotive-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

.automotive-rating, .precision-rating, .tire-rating, .load-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 制造流程 */
.manufacturing-process {
  padding: 80px 0;
  background: white;
}

.manufacturing-process h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.process-flow {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.process-stage {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  position: relative;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.process-stage:hover {
  transform: translateY(-5px);
}

.stage-number {
  position: absolute;
  top: -15px;
  left: 30px;
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
}

.stage-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stage-content h3 {
  color: #1A365D;
  margin-bottom: 15px;
  margin-top: 10px;
  font-size: 1.3rem;
}

.stage-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.stage-specs {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.stage-specs span {
  background: #e3f2fd;
  color: #1976D2;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 汽车成功案例 */
.automotive-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.automotive-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.success-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
  align-items: stretch;
}

.success-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 450px;
}

.success-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.success-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.success-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.success-category {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.success-card.tire-manufacturer .success-category {
  background: linear-gradient(135deg, #388E3C, #66BB6A);
}

.success-card.engine-plant .success-category {
  background: linear-gradient(135deg, #D32F2F, #EF5350);
}

.success-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.success-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.success-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: auto;
  padding-top: 25px;
}

.metric-item {
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 16px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

/* 汽车行业指标增强效果 */
.automotive-industry .metric-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1976D2, #42A5F5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.automotive-industry .metric-item:hover::before {
  opacity: 1;
}

.automotive-industry .metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #1976D2;
}

.metric-number {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1976D2;
  margin-bottom: 6px;
  line-height: 1.1;
  min-height: 2rem;
  text-align: center;
}

/* 小字体数字（用于复杂或长数字） */
.metric-number.metric-small {
  font-size: 1.1rem;
  line-height: 1.2;
  font-weight: 600;
}

/* 确保所有数字容器高度一致 */
.automotive-industry .metric-item {
  min-height: 95px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
  line-height: 1.3;
  text-align: center;
  margin-top: auto;
  padding-top: 4px;
}

/* 确保数字和标签之间的间距一致 */
.automotive-industry .success-metrics .metric-item {
  padding: 18px 10px;
  gap: 8px;
}

/* 为不同卡片组的数字大小进行微调 */
.automotive-industry .success-card.major-oem .metric-number {
  font-size: 1.4rem;
}

.automotive-industry .success-card.tire-manufacturer .metric-number {
  font-size: 1.5rem;
}

.automotive-industry .success-card.engine-plant .metric-number:not(.metric-small) {
  font-size: 1.6rem;
}

/* 汽车行业成功案例响应式设计 */
@media (max-width: 1024px) {
  .automotive-industry .success-card {
    min-height: 420px;
  }

  .automotive-industry .success-metrics {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .automotive-industry .metric-item {
    padding: 14px 6px;
    min-height: 85px;
  }

  .automotive-industry .metric-number {
    font-size: 1.3rem;
  }
}

@media (max-width: 768px) {
  .automotive-industry .success-card {
    min-height: 380px;
  }

  .automotive-industry .success-metrics {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .automotive-industry .metric-item {
    padding: 12px 4px;
    min-height: 80px;
  }

  .automotive-industry .metric-number {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .automotive-industry .success-card {
    min-height: 350px;
  }

  .automotive-industry .success-metrics {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .automotive-industry .metric-item {
    padding: 16px 10px;
    min-height: 90px;
  }

  .automotive-industry .metric-number {
    font-size: 1.4rem;
  }
}

/* 汽车技术规格 */
.automotive-specs {
  background: white;
}

.specs-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 汽车CTA增强 */
.automotive-cta {
  background: linear-gradient(135deg, #1976D2 0%, #42A5F5 50%, #64B5F6 100%);
  position: relative;
  overflow: hidden;
}

.automotive-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.automotive-certifications {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.cert-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.cert-item .cert-icon {
  font-size: 1.2rem;
}

/* 汽车行业响应式设计 */
@media (max-width: 1024px) {
  .automotive-sectors .sectors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .automotive-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .process-flow {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .automotive-sectors .sectors-grid,
  .success-grid {
    grid-template-columns: 1fr;
  }

  .automotive-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .automotive-sectors .sector-card,
  .success-card {
    padding: 25px;
  }

  .process-flow {
    grid-template-columns: 1fr;
  }

  .process-stage {
    padding: 25px;
  }

  .success-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .success-metrics {
    justify-content: space-around;
  }

  .specs-categories {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .automotive-certifications {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .automotive-sectors,
  .manufacturing-process,
  .automotive-success,
  .automotive-specs {
    padding: 60px 0;
  }

  .automotive-sectors .sectors-grid,
  .success-grid {
    gap: 20px;
  }

  .automotive-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .automotive-sectors .sector-card,
  .success-card,
  .process-stage {
    padding: 20px;
  }

  .automotive-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .automotive-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .metric-item {
    min-width: 80px;
  }

  .metric-number {
    font-size: 1.5rem;
  }
}

/* 健身行业页面专用样式 */
.fitness-industry .hero-features .feature-item {
  background: rgba(233,30,99,0.1);
  border: 1px solid rgba(233,30,99,0.3);
}

/* 健身设备分类 */
.fitness-categories {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fce4ec 100%);
}

.fitness-categories h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 健身设备分类优化 - 单排显示 */
.fitness-categories .categories-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.category-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #E91E63, #F06292);
}

.category-card.strength::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.category-card.functional::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.category-card.commercial::before {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.category-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.category-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.category-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.category-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.category-features {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: auto;
}

.category-features li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.category-features li::before {
  content: '💪';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.category-card.cardio .category-features li::before {
  content: '❤️';
}

.category-card.strength .category-features li::before {
  content: '🏋️';
}

.category-card.functional .category-features li::before {
  content: '🤸';
}

.category-card.commercial .category-features li::before {
  content: '🏢';
}

/* 产品解决方案增强 */
.fitness-industry .solution-category.silent-drive .category-header {
  background: linear-gradient(135deg, #E91E63, #F06292);
}

.fitness-industry .solution-category.high-load .category-header {
  background: linear-gradient(135deg, #FF5722, #FF8A65);
}

.fitness-industry .solution-category.long-life .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.fitness-industry .solution-category.precision .category-header {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.noise-rating, .strength-rating, .durability-rating, .control-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 健身设备应用 */
.fitness-applications {
  padding: 80px 0;
  background: white;
}

.fitness-applications h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 健身设备应用展示优化 - 单排显示 */
.fitness-industry .applications-showcase {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.applications-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
}

/* 健身应用卡片优化 */
.fitness-industry .application-item {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: #f8f9fa;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
  height: 100%;
  text-align: center;
}

.application-item {
  display: flex;
  gap: 25px;
  background: #f8f9fa;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

.application-item:hover {
  transform: translateY(-5px);
}

/* 健身应用图标优化 */
.fitness-industry .application-image {
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.application-image {
  flex: 0 0 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.equipment-icon {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fce4ec, #f8bbd9);
}

.application-item.elliptical .equipment-icon {
  background: linear-gradient(135deg, #fff3e0, #ffcc80);
}

.application-item.strength-machine .equipment-icon {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
}

/* 健身应用内容优化 */
.fitness-industry .application-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.application-content {
  flex: 1;
}

.application-content h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.application-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

/* 健身应用规格优化 */
.fitness-industry .application-specs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: auto;
  justify-content: center;
}

.application-specs {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.application-specs span {
  background: #e91e63;
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

.application-item.elliptical .application-specs span {
  background: #ff5722;
}

.application-item.strength-machine .application-specs span {
  background: #4caf50;
}

/* 健身成功案例 */
.fitness-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fce4ec 100%);
}

.fitness-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
  align-items: stretch;
}

.fitness-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 450px;
}

.fitness-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.fitness-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.fitness-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.fitness-success .story-category {
  background: linear-gradient(135deg, #E91E63, #F06292);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.equipment-manufacturer .story-category {
  background: linear-gradient(135deg, #FF5722, #FF8A65);
}

.story-card.rehabilitation .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.fitness-success .story-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.fitness-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.fitness-success .story-results {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: auto;
  padding-top: 25px;
}

.fitness-success .result-item {
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 16px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

/* 健身行业指标增强效果 */
.fitness-success .result-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #E91E63, #F06292);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fitness-success .result-item:hover::before {
  opacity: 1;
}

.fitness-success .result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #E91E63;
}

.fitness-success .result-number {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #E91E63;
  margin-bottom: 6px;
  line-height: 1.1;
  min-height: 2rem;
  text-align: center;
}

/* 小字体数字（用于复杂或长数字） */
.fitness-success .result-number.result-small {
  font-size: 1.1rem;
  line-height: 1.2;
  font-weight: 600;
}

.fitness-success .result-label {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
  line-height: 1.3;
  text-align: center;
  margin-top: auto;
  padding-top: 4px;
}

/* 健身技术规格 */
.fitness-specs {
  background: white;
}

.specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 健身CTA增强 */
.fitness-cta {
  background: linear-gradient(135deg, #E91E63 0%, #F06292 50%, #F48FB1 100%);
  position: relative;
  overflow: hidden;
}

.fitness-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.fitness-benefits {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.benefit-item .benefit-icon {
  font-size: 1.2rem;
}

/* 健身行业响应式设计 */
@media (max-width: 1024px) {
  .fitness-categories .categories-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .fitness-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .fitness-industry .applications-showcase {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .fitness-categories .categories-grid,
  .success-stories {
    grid-template-columns: 1fr;
  }

  .fitness-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .fitness-industry .applications-showcase {
    grid-template-columns: 1fr;
  }

  .category-card,
  .fitness-success .story-card {
    padding: 25px;
  }

  .applications-showcase {
    grid-template-columns: 1fr;
  }

  .application-item {
    flex-direction: column;
    text-align: center;
  }

  .application-image {
    flex: none;
    margin-bottom: 20px;
  }

  .fitness-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .fitness-success .story-card {
    min-height: 420px;
  }

  .fitness-success .story-results {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .fitness-benefits {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .fitness-categories,
  .fitness-applications,
  .fitness-success,
  .fitness-specs {
    padding: 60px 0;
  }

  .fitness-categories .categories-grid,
  .success-stories {
    gap: 20px;
  }

  .fitness-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .fitness-industry .applications-showcase {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .category-card,
  .fitness-success .story-card,
  .application-item {
    padding: 20px;
  }

  .fitness-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .fitness-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .fitness-success .story-card {
    min-height: 380px;
  }

  .fitness-success .story-results {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .fitness-success .result-item {
    padding: 12px 4px;
    min-height: 80px;
  }

  .fitness-success .result-number {
    font-size: 1.2rem;
  }

  .application-specs {
    justify-content: center;
  }
}

/* 健身行业手机端响应式设计 */
@media (max-width: 480px) {
  .fitness-success .story-card {
    min-height: 350px;
  }

  .fitness-success .story-results {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .fitness-success .result-item {
    padding: 16px 10px;
    min-height: 90px;
  }

  .fitness-success .result-number {
    font-size: 1.4rem;
  }

  .fitness-success .result-number.result-small {
    font-size: 1.0rem;
  }
}

/* 制造业页面专用样式 */
.manufacturing-industry .hero-features .feature-item {
  background: rgba(255,106,0,0.1);
  border: 1px solid rgba(255,106,0,0.3);
}

/* 制造业板块 */
.manufacturing-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff3e0 100%);
}

.manufacturing-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 制造业板块优化 - 两行布局 */
.manufacturing-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.manufacturing-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.manufacturing-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #795548, #A1887F);
}

.manufacturing-sectors .sector-card.textile::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.manufacturing-sectors .sector-card.metalworking::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.manufacturing-sectors .sector-card.electronics::before {
  background: linear-gradient(90deg, #3F51B5, #7986CB);
}

.manufacturing-sectors .sector-card.plastics::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.manufacturing-sectors .sector-card.woodworking::before {
  background: linear-gradient(90deg, #8BC34A, #AED581);
}

.manufacturing-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.manufacturing-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.manufacturing-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.manufacturing-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.manufacturing-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: auto;
}

.manufacturing-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.manufacturing-sectors .sector-applications li::before {
  content: '📄';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.manufacturing-sectors .sector-card.textile .sector-applications li::before {
  content: '🧵';
}

.manufacturing-sectors .sector-card.metalworking .sector-applications li::before {
  content: '🔩';
}

.manufacturing-sectors .sector-card.electronics .sector-applications li::before {
  content: '💻';
}

.manufacturing-sectors .sector-card.plastics .sector-applications li::before {
  content: '🔶';
}

.manufacturing-sectors .sector-card.woodworking .sector-applications li::before {
  content: '🌳';
}

/* 制造业产品解决方案优化 - 单排显示 */
.manufacturing-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.manufacturing-industry .solution-category {
  background: #f8f9fa !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.manufacturing-industry .solution-category:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15) !important;
}

/* 产品解决方案增强 */
.manufacturing-industry .solution-category.general-purpose .category-header {
  background: linear-gradient(135deg, #607D8B, #90A4AE);
}

.manufacturing-industry .solution-category.high-performance .category-header {
  background: var(--gradient-manufacturing);
}

.manufacturing-industry .solution-category.specialty .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.manufacturing-industry .solution-category.heavy-duty .category-header {
  background: linear-gradient(135deg, #795548, #A1887F);
}

.versatile-rating, .performance-rating, .specialty-rating, .industrial-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 制造流程集成 */
.manufacturing-integration {
  padding: 80px 0;
  background: white;
}

.manufacturing-integration h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 制造业流程集成优化 - 单排显示 */
.manufacturing-industry .integration-flow {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
}

.integration-flow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 制造业流程阶段优化 */
.manufacturing-industry .flow-stage {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  transition: transform 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.flow-stage {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  position: relative;
}

.flow-stage:hover {
  transform: translateY(-5px);
}

.stage-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.flow-stage h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.flow-stage p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

/* 制造业流程阶段好处优化 */
.manufacturing-industry .stage-benefits {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.stage-benefits {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stage-benefits span {
  background: #fff3e0;
  color: #FF6A00;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 制造成功案例 */
.manufacturing-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff3e0 100%);
}

.manufacturing-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 制造业成功案例优化 - 单排显示 */
.manufacturing-industry .success-showcase {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
}

.success-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

/* 制造业成功案例卡片优化 */
.manufacturing-industry .success-story {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.success-story {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.success-story:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

/* 制造业成功案例头部优化 */
.manufacturing-industry .story-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.manufacturing-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

/* 制造业成功案例标题优化 */
.manufacturing-industry .story-header h3 {
  color: #1A365D;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.manufacturing-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

/* 制造业成功案例分类标签优化 */
.manufacturing-industry .story-category {
  background: linear-gradient(135deg, #795548, #A1887F);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(121, 85, 72, 0.3);
}

.manufacturing-success .story-category {
  background: linear-gradient(135deg, #795548, #A1887F);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 制造业不同类型标签颜色 */
.manufacturing-industry .success-story.electronics-fab .story-category {
  background: linear-gradient(135deg, #3F51B5, #7986CB);
  box-shadow: 0 2px 8px rgba(63, 81, 181, 0.3);
}

.manufacturing-industry .success-story.textile-plant .story-category {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
}

.success-story.electronics-fab .story-category {
  background: linear-gradient(135deg, #3F51B5, #7986CB);
}

.success-story.textile-plant .story-category {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

/* 制造业成功案例内容优化 */
.manufacturing-industry .story-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.manufacturing-industry .story-content p {
  color: #555;
  line-height: 1.7;
  margin-bottom: 18px;
  font-size: 0.95rem;
}

.manufacturing-industry .story-content p strong {
  color: #1A365D;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 5px;
}

.manufacturing-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

/* 制造业成功案例指标优化 */
.manufacturing-industry .story-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: auto;
  padding-top: 25px;
}

.story-metrics {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

/* 制造业指标框优化 */
.manufacturing-industry .metric-box {
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 14px 6px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.manufacturing-industry .metric-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #FF6A00, #FF8533);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.manufacturing-industry .metric-box:hover::before {
  opacity: 1;
}

.manufacturing-industry .metric-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #FF6A00;
}

.metric-box {
  text-align: center;
  flex: 1;
  min-width: 100px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

/* 制造业指标数值优化 */
.manufacturing-industry .metric-value {
  display: block;
  font-size: 1.3rem;
  font-weight: 800;
  color: #FF6A00;
  margin-bottom: 6px;
  line-height: 1.0;
  text-shadow: 0 1px 2px rgba(255, 106, 0, 0.1);
  text-align: center;
  padding: 0 2px;
}

.manufacturing-industry .metric-label {
  font-size: 0.7rem;
  color: #555;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  line-height: 1.3;
  word-break: break-word;
  hyphens: auto;
  padding: 0 2px;
}

.metric-value {
  display: block;
  font-size: 1.6rem;
  font-weight: bold;
  color: #FF6A00;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 制造技术规格 */
.manufacturing-specs {
  background: white;
}

.specs-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 制造CTA增强 */
.manufacturing-cta {
  background: linear-gradient(135deg, #FF6A00 0%, #FF8A50 50%, #FFAB91 100%);
  position: relative;
  overflow: hidden;
}

.manufacturing-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.manufacturing-advantages {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.advantage-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.advantage-item .advantage-icon {
  font-size: 1.2rem;
}

/* 制造业响应式设计 */
@media (max-width: 1024px) {
  .manufacturing-sectors .sectors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .manufacturing-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .manufacturing-industry .integration-flow {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .manufacturing-industry .success-showcase {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .manufacturing-industry .story-metrics {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .manufacturing-industry .metric-box {
    padding: 12px 4px;
    min-height: 80px;
  }

  .manufacturing-industry .metric-value {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .manufacturing-sectors .sectors-grid,
  .success-showcase {
    grid-template-columns: 1fr;
  }

  .manufacturing-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .manufacturing-industry .integration-flow {
    grid-template-columns: 1fr;
  }

  .manufacturing-industry .success-showcase {
    grid-template-columns: 1fr;
  }

  .manufacturing-industry .story-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .manufacturing-industry .story-metrics {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .manufacturing-industry .metric-box {
    padding: 14px 6px;
    min-height: 85px;
  }

  .manufacturing-sectors .sector-card,
  .success-story {
    padding: 25px;
  }

  .integration-flow {
    grid-template-columns: 1fr;
  }

  .flow-stage {
    padding: 25px;
  }

  .manufacturing-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .story-metrics {
    justify-content: space-around;
  }

  .specs-matrix {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .manufacturing-advantages {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .manufacturing-sectors,
  .manufacturing-integration,
  .manufacturing-success,
  .manufacturing-specs {
    padding: 60px 0;
  }

  .manufacturing-sectors .sectors-grid,
  .success-showcase {
    gap: 20px;
  }

  .manufacturing-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .manufacturing-industry .integration-flow {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .manufacturing-industry .success-showcase {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .manufacturing-industry .story-metrics {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .manufacturing-industry .metric-box {
    padding: 16px 10px;
    min-height: 90px;
  }

  .manufacturing-industry .metric-value {
    font-size: 1.4rem;
  }

  .manufacturing-sectors .sector-card,
  .success-story,
  .flow-stage {
    padding: 20px;
  }

  .manufacturing-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .manufacturing-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .metric-box {
    min-width: 80px;
    padding: 12px;
  }

  .metric-value {
    font-size: 1.4rem;
  }

  .story-metrics {
    gap: 15px;
  }
}

/* 铝材行业页面专用样式 */
.aluminum-industry .hero-features .feature-item {
  background: rgba(74,144,226,0.1);
  border: 1px solid rgba(74,144,226,0.3);
}

/* 行业概述部分 */
.industry-overview {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.overview-content {
  text-align: center;
  max-width: 1000px;
  margin: 0 auto;
}

.overview-content h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 30px;
  font-weight: 700;
}

.overview-text {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #4A5568;
  margin-bottom: 50px;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 50px;
}

.stat-item {
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  color: #4A90E2;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 1rem;
  color: #718096;
  font-weight: 500;
}

/* 挑战规格标签 */
.challenge-specs {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.spec-tag {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 陶瓷玻璃行业页面专用样式 */
/* 解决方案类别样式 */
.solution-category.high-temp {
  border-left: 4px solid #FF6B35;
}

.solution-category.chemical-resistant {
  border-left: 4px solid #4A90E2;
}

/* 移除通用 heavy-duty 左边框样式，由各行业单独定义 */

.temp-rating, .chem-rating, .load-rating {
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
}

.chem-rating {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.load-rating {
  background: linear-gradient(135deg, #28A745 0%, #20C997 100%);
}

/* 成功案例样式 */
.success-stories {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.success-stories::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.success-stories .container {
  position: relative;
  z-index: 1;
}

.success-stories h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 20px;
  font-weight: 700;
  position: relative;
}

.success-stories h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #4A90E2 0%, #28A745 100%);
  border-radius: 2px;
}

.success-stories .container::before {
  content: 'Real Customer Success Stories';
  display: block;
  text-align: center;
  font-size: 1rem;
  color: #718096;
  margin-bottom: 50px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.aluminum-industry .stories-grid {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 40px !important;
  max-width: 1000px !important;
  margin: 0 auto !important;
  justify-items: center !important;
}

/* 铝材行业Success Stories结果对齐优化 */
.aluminum-industry .metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  align-items: stretch;
}

.aluminum-industry .result-metric {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px 15px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid transparent;
  min-height: 130px;
  height: 100%;
}

.aluminum-industry .result-metric:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.aluminum-industry .result-metric.primary {
  border-color: #4A90E2;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

.aluminum-industry .result-metric.primary .metric-number {
  color: #4A90E2;
}

.aluminum-industry .metric-number {
  display: block;
  font-size: 2.2rem;
  font-weight: 700;
  color: #28A745;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  line-height: 1;
}

.aluminum-industry .metric-label {
  font-size: 0.9rem;
  color: #4A5568;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  min-height: 2.4em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.aluminum-industry .story-card {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  width: 100% !important;
  max-width: 480px !important;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 铝业Success Stories内容对齐优化 */
.aluminum-industry .story-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.aluminum-industry .story-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 25px;
}

.aluminum-industry .story-challenge,
.aluminum-industry .story-solution {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 10px;
  min-height: 120px;
}

.aluminum-industry .story-challenge {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-left: 4px solid #e53e3e;
}

.aluminum-industry .story-solution {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  border-left: 4px solid #38a169;
}

.aluminum-industry .story-challenge h4,
.aluminum-industry .story-solution h4 {
  color: #1A365D;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.aluminum-industry .story-challenge p,
.aluminum-industry .story-solution p {
  margin: 0;
  line-height: 1.6;
  color: #4A5568;
  font-size: 0.95rem;
  flex: 1;
}

.aluminum-industry .story-results {
  margin-top: auto;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  padding: 25px;
  border: 2px solid #e2e8f0;
}

.aluminum-industry .results-header h4 {
  color: #1A365D;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.story-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4A90E2 0%, #28A745 50%, #FF6B35 100%);
  border-radius: 15px 15px 0 0;
}

.story-header {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 3px solid #f1f3f4;
}

.story-title-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.story-header h3 {
  color: #1A365D;
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
  min-width: 200px;
}

.story-location {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(74,144,226,0.3);
}

.story-details {
  margin-bottom: 30px;
}

.story-challenge,
.story-solution {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 10px;
}

.story-challenge {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-left: 4px solid #e53e3e;
}

.story-solution {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  border-left: 4px solid #38a169;
}

.story-challenge h4,
.story-solution h4 {
  color: #1A365D;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.story-challenge p,
.story-solution p {
  margin: 0;
  line-height: 1.6;
  color: #4A5568;
  font-size: 0.95rem;
}

.story-results {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  padding: 25px;
  border: 2px solid #e2e8f0;
}

.results-header {
  margin-bottom: 20px;
  text-align: center;
}

.results-header h4 {
  color: #1A365D;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.result-metric {
  text-align: center;
  padding: 20px 15px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 120px;
}

.result-metric:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.result-metric.primary {
  border-color: #4A90E2;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

.result-metric.primary .metric-number {
  color: #4A90E2;
}

.metric-number {
  display: block;
  font-size: 2.2rem;
  font-weight: 700;
  color: #28A745;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  line-height: 1;
}

.metric-label {
  font-size: 0.9rem;
  color: #6C757D;
  font-weight: 500;
  line-height: 1.3;
  text-align: center;
}

/* 增强CTA样式 */
.aluminum-cta {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #2C5282 100%);
}

.cta-benefits {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-weight: 500;
}

.benefit-icon {
  font-size: 1.2rem;
}

/* 铝材行业响应式设计 */
@media (max-width: 1024px) {
  .aluminum-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .aluminum-industry .solutions-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 25px !important;
    max-width: 900px !important;
  }

  .process-timeline {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* 铝材行业中等屏幕响应式 */
@media (max-width: 900px) {
  .aluminum-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
    max-width: 600px !important;
  }
}

/* 铝材行业移动端响应式 */
@media (max-width: 768px) {
  .aluminum-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .aluminum-industry .challenge-item {
    padding: 25px 20px;
  }

  .aluminum-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .process-timeline {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .overview-stats {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .overview-content h2 {
    font-size: 2rem;
  }

  .overview-text {
    font-size: 1.1rem;
  }

  .aluminum-industry .stories-grid {
    grid-template-columns: 1fr !important;
    gap: 30px !important;
    max-width: 500px !important;
  }

  .story-card {
    padding: 25px 20px;
  }

  .story-title-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .story-header h3 {
    min-width: auto;
  }

  .story-challenge,
  .story-solution {
    padding: 15px;
  }

  .story-results {
    padding: 20px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .result-metric {
    padding: 15px 12px;
    min-height: 100px;
  }

  .metric-number {
    font-size: 1.8rem;
  }

  /* 铝材行业移动端Success Stories优化 */
  .aluminum-industry .metrics-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .aluminum-industry .result-metric {
    padding: 15px 12px;
    min-height: 110px;
  }

  .aluminum-industry .metric-label {
    font-size: 0.85rem;
    min-height: 2.2em;
  }

  .aluminum-industry .metric-number {
    font-size: 1.8rem;
  }

  .cta-benefits {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }

  .challenge-specs {
    justify-content: center;
  }

  .challenge-item {
    padding: 25px 20px;
    min-height: 280px;
  }

  .process-step {
    padding: 25px 20px;
  }
}

@media (max-width: 900px) {
  .aluminum-industry .stories-grid {
    grid-template-columns: 1fr !important;
    max-width: 600px !important;
    gap: 35px !important;
  }

  .aluminum-industry .story-card {
    max-width: 100% !important;
  }

  .energy-industry .energy-challenges {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 25px !important;
  }

  .energy-industry .stories-grid {
    grid-template-columns: 1fr !important;
    max-width: 600px !important;
    gap: 35px !important;
  }

  .energy-industry .story-card {
    max-width: 100% !important;
  }

  .energy-industry .showcase-item {
    flex-direction: column !important;
    text-align: center !important;
    gap: 20px !important;
    padding: 25px !important;
  }

  .energy-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }
}

@media (max-width: 480px) {
  .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .energy-industry .energy-challenges {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .process-timeline {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .challenge-item {
    min-height: 250px;
  }

  .energy-industry .story-card {
    padding: 25px 20px !important;
  }

  .energy-industry .results-metrics {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .energy-industry .metric {
    padding: 15px 12px !important;
    min-height: 100px !important;
  }

  .energy-industry .metric-number {
    font-size: 1.6rem !important;
  }

  .energy-industry .metric-label {
    font-size: 0.8rem !important;
    min-height: 2em !important;
  }

  .energy-industry .showcase-item {
    padding: 20px !important;
    gap: 15px !important;
  }

  .energy-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .success-stories h2 {
    font-size: 2rem;
  }

  .story-card {
    padding: 30px 20px;
  }
}

/* 陶瓷玻璃行业页面专用样式 */
.ceramic-glass-industry .hero-features .feature-item {
  background: rgba(216,67,21,0.1);
  border: 1px solid rgba(216,67,21,0.3);
}

/* 陶瓷玻璃行业产品解决方案优化 - 单排显示 */
.ceramic-glass-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 陶瓷玻璃行业解决方案卡片布局优化 */
.ceramic-glass-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.ceramic-glass-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.ceramic-glass-industry .product-features {
  flex: 1 !important;
}

.ceramic-glass-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 陶瓷玻璃行业挑战部分 - 单行布局 */
.ceramic-glass-challenges .challenges-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.ceramic-glass-challenges .challenge-card {
  background: white;
  border-radius: 16px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ceramic-glass-challenges .challenge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF6A00, #FF8533);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.ceramic-glass-challenges .challenge-card:hover::before {
  transform: scaleX(1);
}

.ceramic-glass-challenges .challenge-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.ceramic-glass-challenges .challenge-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.ceramic-glass-challenges .challenge-card h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.ceramic-glass-challenges .challenge-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 陶瓷玻璃行业板块 */
.ceramic-glass-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffebee 100%);
}

.ceramic-glass-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.ceramic-glass-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
}

.ceramic-glass-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ceramic-glass-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #D84315, #FF5722);
}

.ceramic-glass-sectors .sector-card.glass-manufacturing::before {
  background: linear-gradient(90deg, #1976D2, #42A5F5);
}

.ceramic-glass-sectors .sector-card.stone-processing::before {
  background: linear-gradient(90deg, #795548, #A1887F);
}

.ceramic-glass-sectors .sector-card.refractory::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.ceramic-glass-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.ceramic-glass-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.ceramic-glass-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.ceramic-glass-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.ceramic-glass-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ceramic-glass-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.ceramic-glass-sectors .sector-applications li::before {
  content: '🏺';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.ceramic-glass-sectors .sector-card.glass-manufacturing .sector-applications li::before {
  content: '🪟';
}

.ceramic-glass-sectors .sector-card.stone-processing .sector-applications li::before {
  content: '🪨';
}

.ceramic-glass-sectors .sector-card.refractory .sector-applications li::before {
  content: '🔥';
}

/* 陶瓷玻璃行业产品解决方案优化 - 单排显示 */
.ceramic-glass-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 产品解决方案增强 */
.ceramic-glass-industry .solution-category.high-temp .category-header {
  background: linear-gradient(135deg, #D84315, #FF5722);
}

.ceramic-glass-industry .solution-category.abrasion-resistant .category-header {
  background: linear-gradient(135deg, #795548, #A1887F);
}

.ceramic-glass-industry .solution-category.chemical-resistant .category-header {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
}

.ceramic-glass-industry .solution-category.precision .category-header {
  background: linear-gradient(135deg, #FF5722, #FF8A65);
}

.temp-rating, .abrasion-rating, .chemical-rating, .precision-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 材料特性 */
.material-properties {
  padding: 80px 0;
  background: white;
}

.material-properties h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.properties-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
}

.property-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
}

.property-card:hover {
  transform: translateY(-5px);
}

.property-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.property-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.property-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.property-specs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.property-specs span {
  background: #ffebee;
  color: #D84315;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.property-card.mechanical .property-specs span {
  background: #e3f2fd;
  color: #1976D2;
}

.property-card.chemical .property-specs span {
  background: #efebe9;
  color: #795548;
}

.property-card.abrasion .property-specs span {
  background: #fff3e0;
  color: #FF5722;
}

/* 陶瓷玻璃成功案例 */
.ceramic-glass-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffebee 100%);
}

.ceramic-glass-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.success-cases {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.case-study {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-study:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.case-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.case-category {
  background: linear-gradient(135deg, #D84315, #FF5722);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.case-study.ceramic-tile .case-category {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
}

.case-study.stone-fabricator .case-category {
  background: linear-gradient(135deg, #795548, #A1887F);
}

.case-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.case-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.result-metric {
  text-align: center;
  flex: 1;
  min-width: 100px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.result-metric .metric-number {
  display: block;
  font-size: 1.6rem;
  font-weight: bold;
  color: #D84315;
  margin-bottom: 5px;
}

.result-metric .metric-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 陶瓷玻璃成功案例优化样式 */
.ceramic-glass-industry .success-showcase {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

/* 陶瓷玻璃成功案例卡片优化 */
.ceramic-glass-industry .success-story {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.ceramic-glass-industry .success-story:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

/* 陶瓷玻璃成功案例头部优化 */
.ceramic-glass-industry .story-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.ceramic-glass-industry .story-header h3 {
  color: #1A365D;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

/* 陶瓷玻璃成功案例分类标签优化 */
.ceramic-glass-industry .story-category {
  background: linear-gradient(135deg, #D84315, #FF5722);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(216, 67, 21, 0.3);
}

.ceramic-glass-industry .success-story.ceramic-tile .story-category {
  background: linear-gradient(135deg, #795548, #A1887F);
  box-shadow: 0 2px 8px rgba(121, 85, 72, 0.3);
}

.ceramic-glass-industry .success-story.stone-fabricator .story-category {
  background: linear-gradient(135deg, #607D8B, #90A4AE);
  box-shadow: 0 2px 8px rgba(96, 125, 139, 0.3);
}

/* 陶瓷玻璃成功案例内容优化 */
.ceramic-glass-industry .story-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ceramic-glass-industry .story-content p {
  color: #555;
  line-height: 1.7;
  margin-bottom: 18px;
  font-size: 0.95rem;
}

.ceramic-glass-industry .story-content p strong {
  color: #1A365D;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 5px;
}

/* 陶瓷玻璃成功案例指标优化 */
.ceramic-glass-industry .story-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: auto;
  padding-top: 25px;
}

/* 陶瓷玻璃指标框优化 */
.ceramic-glass-industry .metric-box {
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 16px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.ceramic-glass-industry .metric-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #D84315, #FF5722);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ceramic-glass-industry .metric-box:hover::before {
  opacity: 1;
}

.ceramic-glass-industry .metric-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #D84315;
}

/* 陶瓷玻璃指标数值优化 */
.ceramic-glass-industry .metric-value {
  display: block;
  font-size: 1.3rem;
  font-weight: 800;
  color: #D84315;
  margin-bottom: 6px;
  line-height: 1.0;
  text-shadow: 0 1px 2px rgba(216, 67, 21, 0.1);
  text-align: center;
  padding: 0 2px;
}

.ceramic-glass-industry .metric-label {
  font-size: 0.7rem;
  color: #555;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  line-height: 1.3;
  word-break: break-word;
  hyphens: auto;
  padding: 0 2px;
}

/* 陶瓷玻璃技术规格 */
.ceramic-glass-specs {
  background: white;
}

.ceramic-glass-specs .specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 陶瓷玻璃CTA增强 */
.ceramic-glass-cta {
  background: linear-gradient(135deg, #D84315 0%, #FF5722 50%, #FF8A65 100%);
  position: relative;
  overflow: hidden;
}

.ceramic-glass-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.extreme-capabilities {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.capability-item .capability-icon {
  font-size: 1.2rem;
}

/* 陶瓷玻璃行业响应式设计 */
@media (max-width: 1024px) {
  .ceramic-glass-challenges .challenges-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .ceramic-glass-sectors .sectors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .ceramic-glass-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .ceramic-glass-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .properties-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .ceramic-glass-industry .success-showcase {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .ceramic-glass-industry .story-metrics {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .ceramic-glass-industry .metric-box {
    padding: 14px 6px;
    min-height: 85px;
  }

  .ceramic-glass-industry .metric-value {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .ceramic-glass-challenges .challenges-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ceramic-glass-challenges .challenge-card {
    padding: 25px 20px;
  }

  .ceramic-glass-sectors .sectors-grid,
  .properties-grid,
  .success-cases {
    grid-template-columns: 1fr;
  }

  .ceramic-glass-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .ceramic-glass-industry .success-showcase {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .ceramic-glass-industry .story-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .ceramic-glass-industry .story-metrics {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .ceramic-glass-industry .metric-box {
    padding: 16px 8px;
    min-height: 85px;
  }

  .ceramic-glass-sectors .sector-card,
  .property-card,
  .case-study {
    padding: 25px;
  }

  .case-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .case-results {
    justify-content: space-around;
  }

  .ceramic-glass-specs .specs-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .extreme-capabilities {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .ceramic-glass-challenges {
    padding: 60px 0;
  }

  .ceramic-glass-challenges .challenges-row {
    gap: 15px;
  }

  .ceramic-glass-challenges .challenge-card {
    padding: 20px 15px;
  }

  .ceramic-glass-sectors,
  .material-properties,
  .ceramic-glass-success,
  .ceramic-glass-specs {
    padding: 60px 0;
  }

  .ceramic-glass-sectors .sectors-grid,
  .properties-grid,
  .success-cases {
    gap: 20px;
  }

  .ceramic-glass-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .ceramic-glass-industry .success-showcase {
    gap: 20px;
  }

  .ceramic-glass-industry .story-metrics {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .ceramic-glass-industry .metric-box {
    padding: 16px 10px;
    min-height: 90px;
  }

  .ceramic-glass-industry .metric-value {
    font-size: 1.4rem;
  }

  .ceramic-glass-sectors .sector-card,
  .property-card,
  .case-study {
    padding: 20px;
  }

  .ceramic-glass-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .ceramic-glass-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .result-metric {
    min-width: 80px;
    padding: 12px;
  }

  .result-metric .metric-number {
    font-size: 1.4rem;
  }

  .case-results {
    gap: 15px;
  }
}

/* 食品行业页面专用样式 */
.food-industry .hero-features .feature-item {
  background: rgba(76,175,80,0.1);
  border: 1px solid rgba(76,175,80,0.3);
}

/* 食品行业挑战部分 - 单行布局 */
.food-challenges .challenges-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.food-challenges .challenge-card {
  background: white;
  border-radius: 16px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.food-challenges .challenge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #81C784);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.food-challenges .challenge-card:hover::before {
  transform: scaleX(1);
}

.food-challenges .challenge-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.food-challenges .challenge-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.food-challenges .challenge-card h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.food-challenges .challenge-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 食品行业板块 */
.food-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
}

.food-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.food-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
}

.food-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.food-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #8BC34A, #AED581);
}

.food-sectors .sector-card.dairy::before {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.food-sectors .sector-card.bakery::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.food-sectors .sector-card.beverage::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.food-sectors .sector-card.fruits-vegetables::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.food-sectors .sector-card.snacks::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.food-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.food-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.food-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.food-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.food-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.food-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.food-sectors .sector-applications li::before {
  content: '🥩';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.food-sectors .sector-card.dairy .sector-applications li::before {
  content: '🥛';
}

.food-sectors .sector-card.bakery .sector-applications li::before {
  content: '🍞';
}

.food-sectors .sector-card.beverage .sector-applications li::before {
  content: '🥤';
}

.food-sectors .sector-card.fruits-vegetables .sector-applications li::before {
  content: '🥕';
}

.food-sectors .sector-card.snacks .sector-applications li::before {
  content: '🍿';
}

/* 食品行业产品解决方案优化 - 单排显示 */
.food-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 食品行业解决方案卡片布局优化 */
.food-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.food-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.food-industry .product-features {
  flex: 1 !important;
}

.food-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 产品解决方案增强 */
.food-industry .solution-category.food-grade .category-header {
  background: var(--gradient-food);
}

.food-industry .solution-category.washdown .category-header {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.food-industry .solution-category.temperature .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.food-industry .solution-category.detection .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.fda-rating, .hygiene-rating, .temp-rating, .detection-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 食品安全标准 */
.food-safety-standards {
  padding: 80px 0;
  background: white;
}

.food-safety-standards h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 食品安全标准网格 - 单行布局 */
.food-safety-standards .standards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
}

/* 通用标准网格（其他行业） */
.standards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

/* 食品安全标准卡片样式 */
.food-safety-standards .standard-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
  border-top: 4px solid #4CAF50;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.food-safety-standards .standard-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

/* 通用标准卡片样式（其他行业） */
.standard-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #4CAF50;
}

/* 食品安全标准卡片颜色主题 */
.food-safety-standards .standard-card.eu {
  border-top-color: #2196F3;
}

.food-safety-standards .standard-card.haccp {
  border-top-color: #FF9800;
}

.food-safety-standards .standard-card.halal-kosher {
  border-top-color: #9C27B0;
}

/* 通用标准卡片颜色（其他行业） */
.standard-card.eu {
  border-top-color: #2196F3;
}

.standard-card.haccp {
  border-top-color: #FF9800;
}

.standard-card.halal-kosher {
  border-top-color: #9C27B0;
}

.standard-card:hover {
  transform: translateY(-5px);
}

.standard-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

/* 食品安全标准卡片内容样式 */
.food-safety-standards .standard-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
  font-weight: 700;
}

.food-safety-standards .standard-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.food-safety-standards .standard-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

/* 通用标准卡片内容样式（其他行业） */
.standard-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.standard-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

/* 食品安全标准详细信息样式 */
.food-safety-standards .standard-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: auto;
}

.food-safety-standards .standard-details span {
  background: #e8f5e8;
  color: #4CAF50;
  padding: 8px 14px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.food-safety-standards .standard-card.eu .standard-details span {
  background: #e3f2fd;
  color: #2196F3;
}

.food-safety-standards .standard-card.haccp .standard-details span {
  background: #fff3e0;
  color: #FF9800;
}

.food-safety-standards .standard-card.halal-kosher .standard-details span {
  background: #f3e5f5;
  color: #9C27B0;
}

/* 通用标准详细信息样式（其他行业） */
.standard-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.standard-details span {
  background: #e8f5e8;
  color: #4CAF50;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.standard-card.eu .standard-details span {
  background: #e3f2fd;
  color: #2196F3;
}

.standard-card.haccp .standard-details span {
  background: #fff3e0;
  color: #FF9800;
}

.standard-card.halal-kosher .standard-details span {
  background: #f3e5f5;
  color: #9C27B0;
}

/* 食品成功案例 */
.food-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
}

.food-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.food-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.food-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.food-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.food-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.food-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.food-success .story-category {
  background: linear-gradient(135deg, #8BC34A, #AED581);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.dairy-producer .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.story-card.bakery-chain .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.food-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.food-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.food-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.food-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 5px;
}

.food-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 食品行业成功案例优化样式 */
.food-industry .success-showcase {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

/* 食品行业成功案例卡片优化 */
.food-industry .success-story {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.food-industry .success-story:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

/* 食品行业成功案例头部优化 */
.food-industry .story-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.food-industry .story-header h3 {
  color: #1A365D;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

/* 食品行业成功案例分类标签优化 */
.food-industry .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.food-industry .success-story.dairy-producer .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.food-industry .success-story.bakery-chain .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

/* 食品行业成功案例内容优化 */
.food-industry .story-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.food-industry .story-content p {
  color: #555;
  line-height: 1.7;
  margin-bottom: 18px;
  font-size: 0.95rem;
}

.food-industry .story-content p strong {
  color: #1A365D;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 5px;
}

/* 食品行业成功案例指标优化 */
.food-industry .story-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: auto;
  padding-top: 25px;
}

/* 食品行业指标框优化 */
.food-industry .metric-box {
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 16px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.food-industry .metric-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #81C784);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.food-industry .metric-box:hover::before {
  opacity: 1;
}

.food-industry .metric-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #4CAF50;
}

/* 食品行业指标数值优化 */
.food-industry .metric-value {
  display: block;
  font-size: 1.3rem;
  font-weight: 800;
  color: #4CAF50;
  margin-bottom: 6px;
  line-height: 1.0;
  text-shadow: 0 1px 2px rgba(76, 175, 80, 0.1);
  text-align: center;
  padding: 0 2px;
}

.food-industry .metric-label {
  font-size: 0.7rem;
  color: #555;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  line-height: 1.3;
  word-break: break-word;
  hyphens: auto;
  padding: 0 2px;
}

/* 食品技术规格 */
.food-specs {
  background: white;
}

.food-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 食品CTA增强 */
.food-cta {
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 50%, #AED581 100%);
  position: relative;
  overflow: hidden;
}

.food-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.food-certifications {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.food-certifications .cert-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.food-certifications .cert-item .cert-icon {
  font-size: 1.2rem;
}

/* 食品行业响应式设计 */
@media (max-width: 1024px) {
  .food-challenges .challenges-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .food-sectors .sectors-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .food-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .food-safety-standards .standards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .food-industry .success-showcase {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .food-industry .story-metrics {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .food-industry .metric-box {
    padding: 14px 6px;
    min-height: 85px;
  }

  .food-industry .metric-value {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .food-challenges .challenges-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .food-challenges .challenge-card {
    padding: 25px 20px;
  }

  .food-sectors .sectors-grid,
  .food-safety-standards .standards-grid,
  .food-success .success-stories {
    grid-template-columns: 1fr;
  }

  .food-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .food-industry .success-showcase {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .food-industry .story-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .food-industry .story-metrics {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .food-industry .metric-box {
    padding: 16px 8px;
    min-height: 85px;
  }

  .food-sectors .sector-card,
  .standard-card,
  .food-success .story-card {
    padding: 25px;
  }

  .food-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .food-success .story-results {
    justify-content: space-around;
  }

  .food-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .food-certifications {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .food-challenges {
    padding: 60px 0;
  }

  .food-challenges .challenges-row {
    gap: 15px;
  }

  .food-challenges .challenge-card {
    padding: 20px 15px;
  }

  .food-sectors,
  .food-safety-standards,
  .food-success,
  .food-specs {
    padding: 60px 0;
  }

  .food-sectors .sectors-grid,
  .food-safety-standards .standards-grid,
  .food-success .success-stories {
    gap: 20px;
  }

  .food-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .food-industry .success-showcase {
    gap: 20px;
  }

  .food-industry .story-metrics {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .food-industry .metric-box {
    padding: 16px 10px;
    min-height: 90px;
  }

  .food-industry .metric-value {
    font-size: 1.4rem;
  }

  .food-sectors .sector-card,
  .standard-card,
  .food-success .story-card {
    padding: 20px;
  }

  .food-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .food-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .food-success .result-item {
    min-width: 80px;
  }

  .food-success .result-number {
    font-size: 1.5rem;
  }
}

/* 医疗行业页面专用样式 */
.medical-industry .hero-features .feature-item {
  background: rgba(33,150,243,0.1);
  border: 1px solid rgba(33,150,243,0.3);
}

/* 医疗行业挑战部分 - 单行布局 */
.medical-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.medical-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #2196F3;
}

.medical-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.medical-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.medical-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 医疗行业板块 */
.medical-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.medical-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.medical-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.medical-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.medical-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.medical-sectors .sector-card.pharmaceutical::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.medical-sectors .sector-card.dental::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.medical-sectors .sector-card.rehabilitation::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.medical-sectors .sector-card.laboratory::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.medical-sectors .sector-card.hospital::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.medical-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.medical-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.medical-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.medical-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.medical-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.medical-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.medical-sectors .sector-applications li::before {
  content: '🏥';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.medical-sectors .sector-card.pharmaceutical .sector-applications li::before {
  content: '💊';
}

.medical-sectors .sector-card.dental .sector-applications li::before {
  content: '🦷';
}

.medical-sectors .sector-card.rehabilitation .sector-applications li::before {
  content: '🏃';
}

.medical-sectors .sector-card.laboratory .sector-applications li::before {
  content: '🔬';
}

.medical-sectors .sector-card.hospital .sector-applications li::before {
  content: '🏨';
}

/* 医疗行业产品解决方案优化 - 单排显示 */
.medical-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 医疗行业解决方案卡片布局优化 */
.medical-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.medical-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.medical-industry .product-features {
  flex: 1 !important;
}

.medical-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 产品解决方案增强 */
.medical-industry .solution-category.biocompatible .category-header {
  background: var(--gradient-medical);
}

.medical-industry .solution-category.sterilizable .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.medical-industry .solution-category.cleanroom .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.medical-industry .solution-category.precision-medical .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.bio-rating, .sterilization-rating, .cleanroom-rating, .precision-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 医疗认证 */
.medical-certifications {
  padding: 80px 0;
  background: white;
}

.medical-certifications h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 医疗认证网格 - 单行布局 */
.medical-certifications .certifications-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
}

/* 通用认证网格（其他行业） */
.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.cert-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #2196F3;
}

.cert-card.iso-10993 {
  border-top-color: #4CAF50;
}

.cert-card.fda-510k {
  border-top-color: #FF9800;
}

.cert-card.ce-marking {
  border-top-color: #9C27B0;
}

.cert-card:hover {
  transform: translateY(-5px);
}

.cert-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.cert-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.cert-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.cert-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cert-details span {
  background: #e3f2fd;
  color: #2196F3;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.cert-card.iso-10993 .cert-details span {
  background: #e8f5e8;
  color: #4CAF50;
}

.cert-card.fda-510k .cert-details span {
  background: #fff3e0;
  color: #FF9800;
}

.cert-card.ce-marking .cert-details span {
  background: #f3e5f5;
  color: #9C27B0;
}

/* 医疗成功案例 */
.medical-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.medical-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

/* 医疗成功案例 - 单行布局 */
.medical-success .success-cases {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
}

/* 通用成功案例网格（其他行业） */
.success-cases {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.medical-success .case-study {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.medical-success .case-study:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.medical-success .case-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.medical-success .case-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.medical-success .case-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.case-study.pharmaceutical-plant .case-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.case-study.diagnostic-imaging .case-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.medical-success .case-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.medical-success .case-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.medical-success .result-metric {
  text-align: center;
  flex: 1;
  min-width: 100px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.medical-success .result-metric .metric-number {
  display: block;
  font-size: 1.6rem;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 5px;
}

.medical-success .result-metric .metric-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 医疗技术规格 */
.medical-specs {
  background: white;
}

.medical-specs .specs-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 医疗CTA增强 */
.medical-cta {
  background: linear-gradient(135deg, #2196F3 0%, #64B5F6 50%, #90CAF9 100%);
  position: relative;
  overflow: hidden;
}

.medical-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.medical-standards {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.standard-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.standard-item .standard-icon {
  font-size: 1.2rem;
}

/* 医疗行业响应式设计 */
@media (max-width: 1024px) {
  .medical-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .medical-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .medical-certifications .certifications-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .medical-success .success-cases {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .medical-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .medical-industry .challenge-item {
    padding: 25px 20px;
  }

  .medical-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .medical-sectors .sectors-grid,
  .medical-certifications .certifications-grid,
  .medical-success .success-cases {
    grid-template-columns: 1fr;
  }

  .medical-sectors .sector-card,
  .cert-card,
  .medical-success .case-study {
    padding: 25px;
  }

  .medical-success .case-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .medical-success .case-results {
    justify-content: space-around;
  }

  .medical-specs .specs-matrix {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .medical-standards {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  /* 所有行业页面小屏幕优化 */
  .medical-sectors,
  .medical-certifications,
  .medical-success,
  .medical-specs,
  .aluminum-industry,
  .appliance-industry,
  .automation-industry,
  .ceramic-glass-industry,
  .construction-industry,
  .elevator-industry,
  .machine-tools-industry,
  .material-handling-industry,
  .textile-industry {
    padding: 60px 0;
  }

  .medical-industry .challenges-grid,
  .aluminum-industry .challenges-grid,
  .appliance-industry .challenges-grid,
  .automation-industry .challenges-grid,
  .construction-industry .challenges-grid,
  .elevator-industry .challenges-grid,
  .machine-tools-industry .challenges-grid,
  .material-handling-industry .challenges-grid,
  .textile-industry .challenges-grid,
  .medical-sectors .sectors-grid,
  .medical-certifications .certifications-grid,
  .medical-success .success-cases {
    gap: 20px;
  }

  .medical-industry .solutions-grid,
  .aluminum-industry .solutions-grid,
  .appliance-industry .solutions-grid,
  .automation-industry .solutions-grid,
  .ceramic-glass-industry .solutions-grid,
  .construction-industry .solutions-grid,
  .elevator-industry .solutions-grid,
  .machine-tools-industry .solutions-grid,
  .material-handling-industry .solutions-grid,
  .textile-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .medical-sectors .sector-card,
  .cert-card,
  .medical-success .case-study {
    padding: 20px;
  }

  .medical-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .medical-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .medical-success .result-metric {
    min-width: 80px;
    padding: 12px;
  }

  .medical-success .result-metric .metric-number {
    font-size: 1.4rem;
  }

  .medical-success .case-results {
    gap: 15px;
  }
}

/* 家电行业页面专用样式 */
.appliance-industry .hero-features .feature-item {
  background: rgba(33,150,243,0.1);
  border: 1px solid rgba(33,150,243,0.3);
}

/* 家电行业挑战部分 - 单行布局 */
.appliance-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.appliance-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #2196F3;
}

.appliance-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.appliance-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.appliance-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 家电行业产品解决方案优化 - 单排显示 */
.appliance-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 家电行业解决方案卡片布局优化 */
.appliance-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.appliance-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.appliance-industry .product-features {
  flex: 1 !important;
}

.appliance-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 家电分类 */
.appliance-categories {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.appliance-categories h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.appliance-categories .categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.appliance-categories .category-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.appliance-categories .category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.appliance-categories .category-card.kitchen::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.appliance-categories .category-card.hvac::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.appliance-categories .category-card.small-appliances::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.appliance-categories .category-card.refrigeration::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.appliance-categories .category-card.cleaning::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.appliance-categories .category-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.appliance-categories .category-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.appliance-categories .category-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.appliance-categories .category-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.appliance-categories .category-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.appliance-categories .category-features li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.appliance-categories .category-features li::before {
  content: '🔧';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.appliance-categories .category-card.laundry .category-features li::before {
  content: '🌊';
}

.appliance-categories .category-card.kitchen .category-features li::before {
  content: '🍽️';
}

.appliance-categories .category-card.hvac .category-features li::before {
  content: '❄️';
}

.appliance-categories .category-card.small-appliances .category-features li::before {
  content: '⚡';
}

.appliance-categories .category-card.refrigeration .category-features li::before {
  content: '🧊';
}

.appliance-categories .category-card.cleaning .category-features li::before {
  content: '🧹';
}

/* 产品解决方案增强 */
.appliance-industry .solution-category.quiet-drive .category-header {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.appliance-industry .solution-category.energy-efficient .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.appliance-industry .solution-category.long-life .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.appliance-industry .solution-category.compact .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.noise-rating, .efficiency-rating, .durability-rating, .size-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 家电设计趋势 */
.appliance-trends {
  padding: 80px 0;
  background: white;
}

.appliance-trends h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.trends-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.trend-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #2196F3;
}

.trend-card.energy-efficiency {
  border-top-color: #4CAF50;
}

.trend-card.quiet-operation {
  border-top-color: #FF9800;
}

.trend-card.compact-design {
  border-top-color: #9C27B0;
}

.trend-card:hover {
  transform: translateY(-5px);
}

.trend-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.trend-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.trend-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.trend-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trend-features span {
  background: #e3f2fd;
  color: #2196F3;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.trend-card.energy-efficiency .trend-features span {
  background: #e8f5e8;
  color: #4CAF50;
}

.trend-card.quiet-operation .trend-features span {
  background: #fff3e0;
  color: #FF9800;
}

.trend-card.compact-design .trend-features span {
  background: #f3e5f5;
  color: #9C27B0;
}

/* 家电成功案例 */
.appliance-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.appliance-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.appliance-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.appliance-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.appliance-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.appliance-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.appliance-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.appliance-success .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.dishwasher-oem .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.story-card.hvac-system .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.appliance-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.appliance-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.appliance-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.appliance-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 5px;
}

.appliance-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 家电技术规格 */
.appliance-specs {
  background: white;
}

.appliance-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 家电CTA增强 */
.appliance-cta {
  background: linear-gradient(135deg, #2196F3 0%, #64B5F6 50%, #90CAF9 100%);
  position: relative;
  overflow: hidden;
}

.appliance-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.appliance-benefits {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.appliance-benefits .benefit-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.appliance-benefits .benefit-item .benefit-icon {
  font-size: 1.2rem;
}

/* 家电行业响应式设计 */
@media (max-width: 1024px) {
  .appliance-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .appliance-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .trends-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .appliance-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .appliance-industry .challenge-item {
    padding: 25px 20px;
  }

  .appliance-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .appliance-categories .categories-grid,
  .trends-grid,
  .appliance-success .success-stories {
    grid-template-columns: 1fr;
  }

  .appliance-categories .category-card,
  .trend-card,
  .appliance-success .story-card {
    padding: 25px;
  }

  .appliance-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .appliance-success .story-results {
    justify-content: space-around;
  }

  .appliance-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .appliance-benefits {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .appliance-categories,
  .appliance-trends,
  .appliance-success,
  .appliance-specs {
    padding: 60px 0;
  }

  .appliance-categories .categories-grid,
  .trends-grid,
  .appliance-success .success-stories {
    gap: 20px;
  }

  .appliance-categories .category-card,
  .trend-card,
  .appliance-success .story-card {
    padding: 20px;
  }

  .appliance-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .appliance-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .appliance-success .result-item {
    min-width: 80px;
  }

  .appliance-success .result-number {
    font-size: 1.5rem;
  }
}

/* 农业行业页面专用样式 */
.agricultural-industry .hero-features .feature-item {
  background: rgba(76,175,80,0.1);
  border: 1px solid rgba(76,175,80,0.3);
}

/* 农业行业挑战部分 - 单行布局 */
.agricultural-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.agricultural-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #4CAF50;
}

.agricultural-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.agricultural-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.agricultural-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 农业行业产品解决方案优化 - 单排显示 */
.agricultural-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 农业行业解决方案卡片布局优化 */
.agricultural-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.agricultural-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.agricultural-industry .product-features {
  flex: 1 !important;
}

/* 农业行业板块 */
.agricultural-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
}

.agricultural-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.agricultural-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.agricultural-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.agricultural-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.agricultural-sectors .sector-card.landscaping::before {
  background: linear-gradient(90deg, #8BC34A, #AED581);
}

.agricultural-sectors .sector-card.greenhouse::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.agricultural-sectors .sector-card.livestock::before {
  background: linear-gradient(90deg, #795548, #A1887F);
}

.agricultural-sectors .sector-card.forestry::before {
  background: linear-gradient(90deg, #2E7D32, #4CAF50);
}

.agricultural-sectors .sector-card.specialty-crops::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.agricultural-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.agricultural-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.agricultural-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.agricultural-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.agricultural-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.agricultural-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.agricultural-sectors .sector-applications li::before {
  content: '🚜';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.agricultural-sectors .sector-card.landscaping .sector-applications li::before {
  content: '🌱';
}

.agricultural-sectors .sector-card.greenhouse .sector-applications li::before {
  content: '🏠';
}

.agricultural-sectors .sector-card.livestock .sector-applications li::before {
  content: '🐄';
}

.agricultural-sectors .sector-card.forestry .sector-applications li::before {
  content: '🌲';
}

.agricultural-sectors .sector-card.specialty-crops .sector-applications li::before {
  content: '🍇';
}

/* 产品解决方案增强 */
.agricultural-industry .solution-category.weather-resistant .category-header {
  background: var(--gradient-food);
}

.agricultural-industry .solution-category.heavy-duty-ag .category-header {
  background: linear-gradient(135deg, #795548, #A1887F);
}

.agricultural-industry .solution-category.low-maintenance .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.agricultural-industry .solution-category.precision-ag .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.weather-rating, .load-rating, .maintenance-rating, .precision-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 季节性需求 */
.seasonal-requirements {
  padding: 80px 0;
  background: white;
}

.seasonal-requirements h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.seasons-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.season-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #4CAF50;
}

.season-card.summer {
  border-top-color: #FF9800;
}

.season-card.autumn {
  border-top-color: #FF5722;
}

.season-card.winter {
  border-top-color: #2196F3;
}

.season-card:hover {
  transform: translateY(-5px);
}

.season-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.season-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.season-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.season-requirements {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.season-requirements span {
  background: #e8f5e8;
  color: #4CAF50;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.season-card.summer .season-requirements span {
  background: #fff3e0;
  color: #FF9800;
}

.season-card.autumn .season-requirements span {
  background: #ffebee;
  color: #FF5722;
}

.season-card.winter .season-requirements span {
  background: #e3f2fd;
  color: #2196F3;
}

/* 农业成功案例 */
.agricultural-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
}

.agricultural-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.agricultural-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.agricultural-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.agricultural-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.agricultural-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.agricultural-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.agricultural-success .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.landscaping-company .story-category {
  background: linear-gradient(135deg, #8BC34A, #AED581);
}

.story-card.dairy-farm .story-category {
  background: linear-gradient(135deg, #795548, #A1887F);
}

.agricultural-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.agricultural-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.agricultural-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.agricultural-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 5px;
}

.agricultural-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 农业技术规格 */
.agricultural-specs {
  background: white;
}

.agricultural-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 农业CTA增强 */
.agricultural-cta {
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 50%, #AED581 100%);
  position: relative;
  overflow: hidden;
}

.agricultural-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.agricultural-advantages {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.agricultural-advantages .advantage-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.agricultural-advantages .advantage-item .advantage-icon {
  font-size: 1.2rem;
}

/* 农业行业响应式设计 */
@media (max-width: 1024px) {
  .seasons-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .agricultural-sectors .sectors-grid,
  .seasons-grid,
  .agricultural-success .success-stories {
    grid-template-columns: 1fr;
  }

  .agricultural-sectors .sector-card,
  .season-card,
  .agricultural-success .story-card {
    padding: 25px;
  }

  .agricultural-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .agricultural-success .story-results {
    justify-content: space-around;
  }

  .agricultural-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .agricultural-advantages {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

/* 农业行业响应式设计 */
@media (max-width: 1024px) {
  .agricultural-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .agricultural-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }
}

@media (max-width: 768px) {
  .agricultural-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .agricultural-industry .challenge-item {
    padding: 25px 20px;
  }

  .agricultural-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }
}

@media (max-width: 480px) {
  .agricultural-sectors,
  .seasonal-requirements,
  .agricultural-success,
  .agricultural-specs {
    padding: 60px 0;
  }

  .agricultural-sectors .sectors-grid,
  .seasons-grid,
  .agricultural-success .success-stories {
    gap: 20px;
  }

  .agricultural-sectors .sector-card,
  .season-card,
  .agricultural-success .story-card {
    padding: 20px;
  }

  .agricultural-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .agricultural-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .agricultural-success .result-item {
    min-width: 80px;
  }

  .agricultural-success .result-number {
    font-size: 1.5rem;
  }
}

/* 自动化行业页面专用样式 */
.automation-industry .hero-features .feature-item {
  background: rgba(255,87,34,0.1);
  border: 1px solid rgba(255,87,34,0.3);
}

/* 自动化行业挑战部分 - 单行布局 */
.automation-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.automation-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #FF5722;
}

.automation-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.automation-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.automation-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 自动化行业产品解决方案优化 - 单排显示 */
.automation-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 自动化行业解决方案卡片布局优化 */
.automation-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.automation-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.automation-industry .product-features {
  flex: 1 !important;
}

.automation-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 自动化行业板块 */
.automation-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffebee 100%);
}

.automation-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.automation-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.automation-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.automation-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.automation-sectors .sector-card.manufacturing-automation::before {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.automation-sectors .sector-card.material-handling::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.automation-sectors .sector-card.process-automation::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.automation-sectors .sector-card.semiconductor::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.automation-sectors .sector-card.laboratory-automation::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.automation-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.automation-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.automation-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.automation-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.automation-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.automation-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.automation-sectors .sector-applications li::before {
  content: '🤖';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.automation-sectors .sector-card.manufacturing-automation .sector-applications li::before {
  content: '🏭';
}

.automation-sectors .sector-card.material-handling .sector-applications li::before {
  content: '📦';
}

.automation-sectors .sector-card.process-automation .sector-applications li::before {
  content: '⚗️';
}

.automation-sectors .sector-card.semiconductor .sector-applications li::before {
  content: '💾';
}

.automation-sectors .sector-card.laboratory-automation .sector-applications li::before {
  content: '🔬';
}

/* 产品解决方案增强 */
.automation-industry .solution-category.ultra-precision .category-header {
  background: linear-gradient(135deg, #FF5722, #FF8A65);
}

.automation-industry .solution-category.high-speed .category-header {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.automation-industry .solution-category.continuous-duty .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.automation-industry .solution-category.smart-belts .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.precision-rating, .speed-rating, .reliability-rating, .smart-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 工业4.0技术 */
.industry-4-technologies {
  padding: 80px 0;
  background: white;
}

.industry-4-technologies h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.technologies-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.tech-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #2196F3;
}

.tech-card.predictive-maintenance {
  border-top-color: #4CAF50;
}

.tech-card.digital-twin {
  border-top-color: #FF9800;
}

.tech-card.adaptive-control {
  border-top-color: #9C27B0;
}

.tech-card:hover {
  transform: translateY(-5px);
}

.tech-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.tech-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.tech-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.tech-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tech-features span {
  background: #e3f2fd;
  color: #2196F3;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.tech-card.predictive-maintenance .tech-features span {
  background: #e8f5e8;
  color: #4CAF50;
}

.tech-card.digital-twin .tech-features span {
  background: #fff3e0;
  color: #FF9800;
}

.tech-card.adaptive-control .tech-features span {
  background: #f3e5f5;
  color: #9C27B0;
}

/* 自动化成功案例 */
.automation-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffebee 100%);
}

.automation-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.automation-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.automation-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.automation-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.automation-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.automation-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.automation-success .story-category {
  background: linear-gradient(135deg, #FF5722, #FF8A65);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.semiconductor-fab .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.story-card.pharma-automation .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.automation-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.automation-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.automation-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.automation-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #FF5722;
  margin-bottom: 5px;
}

.automation-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 自动化技术规格 */
.automation-specs {
  background: white;
}

.automation-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 自动化CTA增强 */
.automation-cta {
  background: linear-gradient(135deg, #FF5722 0%, #FF8A65 50%, #FFAB91 100%);
  position: relative;
  overflow: hidden;
}

.automation-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.automation-capabilities {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.automation-capabilities .capability-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.automation-capabilities .capability-item .capability-icon {
  font-size: 1.2rem;
}

/* 自动化行业响应式设计 */
@media (max-width: 1024px) {
  .automation-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .automation-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .technologies-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .automation-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .automation-industry .challenge-item {
    padding: 25px 20px;
  }

  .automation-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .automation-sectors .sectors-grid,
  .technologies-grid,
  .automation-success .success-stories {
    grid-template-columns: 1fr;
  }

  .automation-sectors .sector-card,
  .tech-card,
  .automation-success .story-card {
    padding: 25px;
  }

  .automation-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .automation-success .story-results {
    justify-content: space-around;
  }

  .automation-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .automation-capabilities {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .automation-sectors,
  .industry-4-technologies,
  .automation-success,
  .automation-specs {
    padding: 60px 0;
  }

  .automation-sectors .sectors-grid,
  .technologies-grid,
  .automation-success .success-stories {
    gap: 20px;
  }

  .automation-sectors .sector-card,
  .tech-card,
  .automation-success .story-card {
    padding: 20px;
  }

  .automation-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .automation-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .automation-success .result-item {
    min-width: 80px;
  }

  .automation-success .result-number {
    font-size: 1.5rem;
  }
}

/* 建筑重工业页面专用样式 */
.construction-industry .hero-features .feature-item {
  background: rgba(255,152,0,0.1);
  border: 1px solid rgba(255,152,0,0.3);
}

/* 建筑重工业挑战部分 - 单行布局 */
.construction-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.construction-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #FF9800;
}

.construction-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.construction-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.construction-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 建筑重工业产品解决方案优化 - 单排显示 */
.construction-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 建筑重工业解决方案卡片布局优化 */
.construction-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.construction-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.construction-industry .product-features {
  flex: 1 !important;
}

.construction-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 建筑重工业板块 */
.construction-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff3e0 100%);
}

.construction-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.construction-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.construction-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.construction-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.construction-sectors .sector-card.mining-equipment::before {
  background: linear-gradient(90deg, #795548, #A1887F);
}

.construction-sectors .sector-card.steel-production::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.construction-sectors .sector-card.cement-production::before {
  background: linear-gradient(90deg, #9E9E9E, #BDBDBD);
}

.construction-sectors .sector-card.power-generation::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.construction-sectors .sector-card.oil-gas::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.construction-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.construction-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.construction-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.construction-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.construction-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.construction-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.construction-sectors .sector-applications li::before {
  content: '🏗️';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.construction-sectors .sector-card.mining-equipment .sector-applications li::before {
  content: '⛏️';
}

.construction-sectors .sector-card.steel-production .sector-applications li::before {
  content: '🔥';
}

.construction-sectors .sector-card.cement-production .sector-applications li::before {
  content: '🏭';
}

.construction-sectors .sector-card.power-generation .sector-applications li::before {
  content: '⚡';
}

.construction-sectors .sector-card.oil-gas .sector-applications li::before {
  content: '🛢️';
}

/* 产品解决方案增强 */
.construction-industry .solution-category.ultra-heavy-duty .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.construction-industry .solution-category.extreme-environment .category-header {
  background: linear-gradient(135deg, #795548, #A1887F);
}

.construction-industry .solution-category.extended-life .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.construction-industry .solution-category.safety-certified .category-header {
  background: linear-gradient(135deg, #FF5722, #FF8A65);
}

.power-rating, .environment-rating, .life-rating, .safety-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 安全标准 */
.safety-standards {
  padding: 80px 0;
  background: white;
}

.safety-standards h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.standards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.standard-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #FF9800;
}

.standard-card.iecex-certification {
  border-top-color: #2196F3;
}

.standard-card.msha-approval {
  border-top-color: #795548;
}

.standard-card.osha-compliance {
  border-top-color: #4CAF50;
}

.standard-card:hover {
  transform: translateY(-5px);
}

.standard-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.standard-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.standard-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.standard-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.standard-details span {
  background: #fff3e0;
  color: #FF9800;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.standard-card.iecex-certification .standard-details span {
  background: #e3f2fd;
  color: #2196F3;
}

.standard-card.msha-approval .standard-details span {
  background: #efebe9;
  color: #795548;
}

.standard-card.osha-compliance .standard-details span {
  background: #e8f5e8;
  color: #4CAF50;
}

/* 建筑成功案例 */
.construction-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff3e0 100%);
}

.construction-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.construction-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.construction-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.construction-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.construction-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.construction-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.construction-success .story-category {
  background: linear-gradient(135deg, #795548, #A1887F);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.steel-mill .story-category {
  background: linear-gradient(135deg, #607D8B, #90A4AE);
}

.story-card.power-plant .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.construction-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.construction-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.construction-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.construction-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #FF9800;
  margin-bottom: 5px;
}

.construction-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 建筑技术规格 */
.construction-specs {
  background: white;
}

.construction-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 建筑CTA增强 */
.construction-cta {
  background: linear-gradient(135deg, #FF9800 0%, #FFB74D 50%, #FFCC02 100%);
  position: relative;
  overflow: hidden;
}

.construction-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.construction-strengths {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.construction-strengths .strength-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.construction-strengths .strength-item .strength-icon {
  font-size: 1.2rem;
}

/* 建筑重工业响应式设计 */
@media (max-width: 1024px) {
  .construction-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .construction-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .standards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .construction-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .construction-industry .challenge-item {
    padding: 25px 20px;
  }

  .construction-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .construction-sectors .sectors-grid,
  .standards-grid,
  .construction-success .success-stories {
    grid-template-columns: 1fr;
  }

  .construction-sectors .sector-card,
  .standard-card,
  .construction-success .story-card {
    padding: 25px;
  }

  .construction-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .construction-success .story-results {
    justify-content: space-around;
  }

  .construction-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .construction-strengths {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .construction-sectors,
  .safety-standards,
  .construction-success,
  .construction-specs {
    padding: 60px 0;
  }

  .construction-sectors .sectors-grid,
  .standards-grid,
  .construction-success .success-stories {
    gap: 20px;
  }

  .construction-sectors .sector-card,
  .standard-card,
  .construction-success .story-card {
    padding: 20px;
  }

  .construction-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .construction-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .construction-success .result-item {
    min-width: 80px;
  }

  .construction-success .result-number {
    font-size: 1.5rem;
  }
}

/* 机床行业页面专用样式 */
.machine-tools-industry .hero-features .feature-item {
  background: rgba(33,150,243,0.1);
  border: 1px solid rgba(33,150,243,0.3);
}

/* 机床行业挑战部分 - 单行布局 */
.machine-tools-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.machine-tools-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #2196F3;
}

.machine-tools-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.machine-tools-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.machine-tools-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 机床行业产品解决方案优化 - 单排显示 */
.machine-tools-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 机床行业解决方案卡片布局优化 */
.machine-tools-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.machine-tools-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.machine-tools-industry .product-features {
  flex: 1 !important;
}

.machine-tools-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 机床分类 */
.machine-tools-categories {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.machine-tools-categories h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.machine-tools-categories .categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.machine-tools-categories .category-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.machine-tools-categories .category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.machine-tools-categories .category-card.turning-lathes::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.machine-tools-categories .category-card.grinding-machines::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.machine-tools-categories .category-card.drilling-boring::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.machine-tools-categories .category-card.edm-machines::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.machine-tools-categories .category-card.automation-systems::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.machine-tools-categories .category-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.machine-tools-categories .category-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.machine-tools-categories .category-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.machine-tools-categories .category-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.machine-tools-categories .category-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.machine-tools-categories .category-features li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.machine-tools-categories .category-features li::before {
  content: '⚙️';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.machine-tools-categories .category-card.turning-lathes .category-features li::before {
  content: '🔄';
}

.machine-tools-categories .category-card.grinding-machines .category-features li::before {
  content: '💎';
}

.machine-tools-categories .category-card.drilling-boring .category-features li::before {
  content: '🔩';
}

.machine-tools-categories .category-card.edm-machines .category-features li::before {
  content: '⚡';
}

.machine-tools-categories .category-card.automation-systems .category-features li::before {
  content: '🤖';
}

/* 产品解决方案增强 */
.machine-tools-industry .solution-category.precision-machining .category-header {
  background: var(--gradient-medical);
}

.machine-tools-industry .solution-category.vibration-damped .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.machine-tools-industry .solution-category.high-speed .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.machine-tools-industry .solution-category.sealed-protection .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.precision-rating, .vibration-rating, .speed-rating, .protection-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 精度要求 */
.precision-requirements {
  padding: 80px 0;
  background: white;
}

.precision-requirements h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.precision-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.precision-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #2196F3;
}

.precision-card.surface-finish {
  border-top-color: #4CAF50;
}

.precision-card.thermal-stability {
  border-top-color: #FF9800;
}

.precision-card.dynamic-response {
  border-top-color: #9C27B0;
}

.precision-card:hover {
  transform: translateY(-5px);
}

.precision-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.precision-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.precision-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.precision-specs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.precision-specs span {
  background: #e3f2fd;
  color: #2196F3;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.precision-card.surface-finish .precision-specs span {
  background: #e8f5e8;
  color: #4CAF50;
}

.precision-card.thermal-stability .precision-specs span {
  background: #fff3e0;
  color: #FF9800;
}

.precision-card.dynamic-response .precision-specs span {
  background: #f3e5f5;
  color: #9C27B0;
}

/* 机床成功案例 */
.machine-tools-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.machine-tools-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.machine-tools-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.machine-tools-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.machine-tools-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.machine-tools-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.machine-tools-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.machine-tools-success .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.automotive-tooling .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.story-card.precision-grinding .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.machine-tools-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.machine-tools-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.machine-tools-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.machine-tools-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 5px;
}

.machine-tools-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 机床技术规格 */
.machine-tools-specs {
  background: white;
}

.machine-tools-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 机床CTA增强 */
.machine-tools-cta {
  background: linear-gradient(135deg, #2196F3 0%, #64B5F6 50%, #90CAF9 100%);
  position: relative;
  overflow: hidden;
}

.machine-tools-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.machine-tools-precision {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.machine-tools-precision .precision-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.machine-tools-precision .precision-item .precision-icon {
  font-size: 1.2rem;
}

/* 机床行业响应式设计 */
@media (max-width: 1024px) {
  .machine-tools-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .machine-tools-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .precision-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .machine-tools-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .machine-tools-industry .challenge-item {
    padding: 25px 20px;
  }

  .machine-tools-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .machine-tools-categories .categories-grid,
  .precision-grid,
  .machine-tools-success .success-stories {
    grid-template-columns: 1fr;
  }

  .machine-tools-categories .category-card,
  .precision-card,
  .machine-tools-success .story-card {
    padding: 25px;
  }

  .machine-tools-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .machine-tools-success .story-results {
    justify-content: space-around;
  }

  .machine-tools-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .machine-tools-precision {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .machine-tools-categories,
  .precision-requirements,
  .machine-tools-success,
  .machine-tools-specs {
    padding: 60px 0;
  }

  .machine-tools-categories .categories-grid,
  .precision-grid,
  .machine-tools-success .success-stories {
    gap: 20px;
  }

  .machine-tools-categories .category-card,
  .precision-card,
  .machine-tools-success .story-card {
    padding: 20px;
  }

  .machine-tools-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .machine-tools-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .machine-tools-success .result-item {
    min-width: 80px;
  }

  .machine-tools-success .result-number {
    font-size: 1.5rem;
  }
}

/* 纺织行业页面专用样式 */
.textile-industry .hero-features .feature-item {
  background: rgba(233,30,99,0.1);
  border: 1px solid rgba(233,30,99,0.3);
}

/* 纺织行业挑战部分 - 单行布局 */
.textile-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.textile-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #E91E63;
}

.textile-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.textile-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.textile-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 纺织行业产品解决方案优化 - 单排显示 */
.textile-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 纺织行业解决方案卡片布局优化 */
.textile-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.textile-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.textile-industry .product-features {
  flex: 1 !important;
}

.textile-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 纺织行业板块 */
.textile-sectors {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fce4ec 100%);
}

.textile-sectors h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.textile-sectors .sectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.textile-sectors .sector-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.textile-sectors .sector-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #E91E63, #F06292);
}

.textile-sectors .sector-card.weaving-machinery::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.textile-sectors .sector-card.knitting-machines::before {
  background: linear-gradient(90deg, #3F51B5, #7986CB);
}

.textile-sectors .sector-card.dyeing-finishing::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.textile-sectors .sector-card.nonwoven-production::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.textile-sectors .sector-card.technical-textiles::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.textile-sectors .sector-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.textile-sectors .sector-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.textile-sectors .sector-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.textile-sectors .sector-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.textile-sectors .sector-applications {
  list-style: none;
  padding: 0;
  margin: 0;
}

.textile-sectors .sector-applications li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.textile-sectors .sector-applications li::before {
  content: '🧵';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.textile-sectors .sector-card.weaving-machinery .sector-applications li::before {
  content: '🕸️';
}

.textile-sectors .sector-card.knitting-machines .sector-applications li::before {
  content: '🧶';
}

.textile-sectors .sector-card.dyeing-finishing .sector-applications li::before {
  content: '🎨';
}

.textile-sectors .sector-card.nonwoven-production .sector-applications li::before {
  content: '🔬';
}

.textile-sectors .sector-card.technical-textiles .sector-applications li::before {
  content: '⚙️';
}

/* 产品解决方案增强 */
.textile-industry .solution-category.tension-control .category-header {
  background: linear-gradient(135deg, #E91E63, #F06292);
}

.textile-industry .solution-category.high-speed-textile .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.textile-industry .solution-category.lint-resistant .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.textile-industry .solution-category.chemical-resistant .category-header {
  background: linear-gradient(135deg, #FF5722, #FF8A65);
}

.tension-rating, .speed-rating, .protection-rating, .chemical-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 纺织工艺流程 */
.textile-process {
  padding: 80px 0;
  background: white;
}

.textile-process h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.process-flow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.process-step {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  position: relative;
  transition: transform 0.3s ease;
}

.process-step:hover {
  transform: translateY(-5px);
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: #E91E63;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.process-step.spinning .step-number {
  background: #9C27B0;
}

.process-step.fabric-formation .step-number {
  background: #3F51B5;
}

.process-step.finishing .step-number {
  background: #FF5722;
}

.step-icon {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.process-step h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.process-step p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.step-requirements {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-requirements span {
  background: rgba(233,30,99,0.1);
  color: #E91E63;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.process-step.spinning .step-requirements span {
  background: rgba(156,39,176,0.1);
  color: #9C27B0;
}

.process-step.fabric-formation .step-requirements span {
  background: rgba(63,81,181,0.1);
  color: #3F51B5;
}

.process-step.finishing .step-requirements span {
  background: rgba(255,87,34,0.1);
  color: #FF5722;
}

/* 纺织成功案例 */
.textile-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fce4ec 100%);
}

.textile-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.textile-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.textile-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.textile-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.textile-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.textile-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.textile-success .story-category {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.nonwoven-producer .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.story-card.carpet-manufacturer .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.textile-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.textile-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.textile-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.textile-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #E91E63;
  margin-bottom: 5px;
}

.textile-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 纺织技术规格 */
.textile-specs {
  background: white;
}

.textile-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 纺织CTA增强 */
.textile-cta {
  background: linear-gradient(135deg, #E91E63 0%, #F06292 50%, #F8BBD9 100%);
  position: relative;
  overflow: hidden;
}

.textile-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.textile-advantages {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.textile-advantages .advantage-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.textile-advantages .advantage-item .advantage-icon {
  font-size: 1.2rem;
}

/* 纺织行业响应式设计 */
@media (max-width: 1024px) {
  .textile-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .textile-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }


}

@media (max-width: 768px) {
  .textile-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .textile-industry .challenge-item {
    padding: 25px 20px;
  }

  .textile-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .textile-sectors .sectors-grid,
  .process-flow,
  .textile-success .success-stories {
    grid-template-columns: 1fr;
  }

  .textile-sectors .sector-card,
  .process-step,
  .textile-success .story-card {
    padding: 25px;
  }

  .textile-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .textile-success .story-results {
    justify-content: space-around;
  }

  .textile-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .textile-advantages {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .process-flow {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .textile-sectors,
  .textile-process,
  .textile-success,
  .textile-specs {
    padding: 60px 0;
  }

  .textile-sectors .sectors-grid,
  .process-flow,
  .textile-success .success-stories {
    gap: 20px;
  }

  .textile-sectors .sector-card,
  .process-step,
  .textile-success .story-card {
    padding: 20px;
  }

  .textile-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .textile-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .textile-success .result-item {
    min-width: 80px;
  }

  .textile-success .result-number {
    font-size: 1.5rem;
  }

  .process-flow {
    grid-template-columns: 1fr;
  }

  .process-step {
    padding: 25px 20px;
  }
}

/* 电梯行业页面专用样式 */
.elevator-industry .hero-features .feature-item {
  background: rgba(33,150,243,0.1);
  border: 1px solid rgba(33,150,243,0.3);
}

/* 电梯行业挑战部分 - 单行布局 */
.elevator-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.elevator-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #2196F3;
}

.elevator-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.elevator-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.elevator-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 电梯行业产品解决方案优化 - 单排显示 */
.elevator-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 电梯行业解决方案卡片布局优化 */
.elevator-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.elevator-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.elevator-industry .product-features {
  flex: 1 !important;
}

.elevator-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 电梯系统 */
.elevator-systems {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.elevator-systems h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.elevator-systems .systems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.elevator-systems .system-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.elevator-systems .system-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.elevator-systems .system-card.freight-elevators::before {
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.elevator-systems .system-card.escalators::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.elevator-systems .system-card.home-elevators::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.elevator-systems .system-card.special-elevators::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.elevator-systems .system-card.maintenance-modernization::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.elevator-systems .system-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.elevator-systems .system-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.elevator-systems .system-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.elevator-systems .system-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.elevator-systems .system-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.elevator-systems .system-features li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.elevator-systems .system-features li::before {
  content: '🏢';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.elevator-systems .system-card.freight-elevators .system-features li::before {
  content: '📦';
}

.elevator-systems .system-card.escalators .system-features li::before {
  content: '🚶';
}

.elevator-systems .system-card.home-elevators .system-features li::before {
  content: '🏠';
}

.elevator-systems .system-card.special-elevators .system-features li::before {
  content: '🏥';
}

.elevator-systems .system-card.maintenance-modernization .system-features li::before {
  content: '🔧';
}

/* 产品解决方案增强 */
.elevator-industry .solution-category.safety-certified .category-header {
  background: var(--gradient-medical);
}

.elevator-industry .solution-category.ultra-quiet .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.elevator-industry .solution-category.energy-efficient .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.elevator-industry .solution-category.compact-design .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.safety-rating, .noise-rating, .efficiency-rating, .space-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 电梯安全标准 */
.elevator-safety-standards {
  padding: 80px 0;
  background: white;
}

.elevator-safety-standards h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.elevator-safety-standards .standards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.elevator-safety-standards .standard-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease;
  border-top: 4px solid #2196F3;
}

.elevator-safety-standards .standard-card.asme-a17 {
  border-top-color: #FF9800;
}

.elevator-safety-standards .standard-card.iso-standards {
  border-top-color: #4CAF50;
}

.elevator-safety-standards .standard-card.local-codes {
  border-top-color: #9C27B0;
}

.elevator-safety-standards .standard-card:hover {
  transform: translateY(-5px);
}

.elevator-safety-standards .standard-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.elevator-safety-standards .standard-card h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.elevator-safety-standards .standard-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.elevator-safety-standards .standard-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.elevator-safety-standards .standard-details span {
  background: #e3f2fd;
  color: #2196F3;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.elevator-safety-standards .standard-card.asme-a17 .standard-details span {
  background: #fff3e0;
  color: #FF9800;
}

.elevator-safety-standards .standard-card.iso-standards .standard-details span {
  background: #e8f5e8;
  color: #4CAF50;
}

.elevator-safety-standards .standard-card.local-codes .standard-details span {
  background: #f3e5f5;
  color: #9C27B0;
}

/* 电梯成功案例 */
.elevator-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.elevator-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.elevator-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.elevator-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.elevator-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.elevator-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.elevator-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.elevator-success .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.hospital-complex .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.story-card.green-building .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.elevator-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.elevator-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.elevator-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.elevator-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 5px;
}

.elevator-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 电梯技术规格 */
.elevator-specs {
  background: white;
}

.elevator-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 电梯CTA增强 */
.elevator-cta {
  background: linear-gradient(135deg, #2196F3 0%, #64B5F6 50%, #90CAF9 100%);
  position: relative;
  overflow: hidden;
}

.elevator-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.elevator-excellence {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.elevator-excellence .excellence-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.elevator-excellence .excellence-item .excellence-icon {
  font-size: 1.2rem;
}

/* 电梯行业响应式设计 */
@media (max-width: 1024px) {
  .elevator-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .elevator-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }

  .elevator-safety-standards .standards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .elevator-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .elevator-industry .challenge-item {
    padding: 25px 20px;
  }

  .elevator-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .elevator-systems .systems-grid,
  .elevator-safety-standards .standards-grid,
  .elevator-success .success-stories {
    grid-template-columns: 1fr;
  }

  .elevator-systems .system-card,
  .elevator-safety-standards .standard-card,
  .elevator-success .story-card {
    padding: 25px;
  }

  .elevator-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .elevator-success .story-results {
    justify-content: space-around;
  }

  .elevator-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .elevator-excellence {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .elevator-systems,
  .elevator-safety-standards,
  .elevator-success,
  .elevator-specs {
    padding: 60px 0;
  }

  .elevator-systems .systems-grid,
  .elevator-safety-standards .standards-grid,
  .elevator-success .success-stories {
    gap: 20px;
  }

  .elevator-systems .system-card,
  .elevator-safety-standards .standard-card,
  .elevator-success .story-card {
    padding: 20px;
  }

  .elevator-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .elevator-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .elevator-success .result-item {
    min-width: 80px;
  }

  .elevator-success .result-number {
    font-size: 1.5rem;
  }
}

/* 物料搬运行业页面专用样式 */
.material-handling-industry .hero-features .feature-item {
  background: rgba(255,152,0,0.1);
  border: 1px solid rgba(255,152,0,0.3);
}

/* 物料搬运行业挑战部分 - 单行布局 */
.material-handling-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.material-handling-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #FF9800;
}

.material-handling-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.material-handling-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.material-handling-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 物料搬运行业产品解决方案优化 - 单排显示 */
.material-handling-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 物料搬运行业解决方案卡片布局优化 */
.material-handling-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.material-handling-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.material-handling-industry .product-features {
  flex: 1 !important;
}

.material-handling-industry .applications {
  margin-top: auto !important;
  padding-top: 15px !important;
  border-top: 1px solid #eee !important;
}

/* 物料搬运系统 */
.material-handling-systems {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff3e0 100%);
}

.material-handling-systems h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.material-handling-systems .systems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.material-handling-systems .system-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.material-handling-systems .system-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.material-handling-systems .system-card.warehouse-automation::before {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.material-handling-systems .system-card.bulk-handling::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.material-handling-systems .system-card.packaging-systems::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.material-handling-systems .system-card.airport-baggage::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.material-handling-systems .system-card.food-beverage::before {
  background: linear-gradient(90deg, #607D8B, #90A4AE);
}

.material-handling-systems .system-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.material-handling-systems .system-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.material-handling-systems .system-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.material-handling-systems .system-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.material-handling-systems .system-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.material-handling-systems .system-features li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.material-handling-systems .system-features li::before {
  content: '📦';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.material-handling-systems .system-card.warehouse-automation .system-features li::before {
  content: '🤖';
}

.material-handling-systems .system-card.bulk-handling .system-features li::before {
  content: '⚖️';
}

.material-handling-systems .system-card.packaging-systems .system-features li::before {
  content: '📦';
}

.material-handling-systems .system-card.airport-baggage .system-features li::before {
  content: '✈️';
}

.material-handling-systems .system-card.food-beverage .system-features li::before {
  content: '🍽️';
}

/* 产品解决方案增强 */
.material-handling-industry .solution-category.continuous-duty .category-header {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.material-handling-industry .solution-category.variable-load .category-header {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.material-handling-industry .solution-category.environmental-resistant .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.material-handling-industry .solution-category.energy-efficient .category-header {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.duty-rating, .load-rating, .environment-rating, .efficiency-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 物料流程 */
.material-flow-process {
  padding: 80px 0;
  background: white;
}

.material-flow-process h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.material-flow-process .process-flow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.material-flow-process .process-step {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  position: relative;
  transition: transform 0.3s ease;
}

.material-flow-process .process-step:hover {
  transform: translateY(-5px);
}

.material-flow-process .step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: #FF9800;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.material-flow-process .process-step.storage .step-number {
  background: #2196F3;
}

.material-flow-process .process-step.processing .step-number {
  background: #4CAF50;
}

.material-flow-process .process-step.shipping .step-number {
  background: #9C27B0;
}

.material-flow-process .step-icon {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.material-flow-process .process-step h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.material-flow-process .process-step p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.material-flow-process .step-requirements {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.material-flow-process .step-requirements span {
  background: rgba(255,152,0,0.1);
  color: #FF9800;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.material-flow-process .process-step.storage .step-requirements span {
  background: rgba(33,150,243,0.1);
  color: #2196F3;
}

.material-flow-process .process-step.processing .step-requirements span {
  background: rgba(76,175,80,0.1);
  color: #4CAF50;
}

.material-flow-process .process-step.shipping .step-requirements span {
  background: rgba(156,39,176,0.1);
  color: #9C27B0;
}

/* 物料搬运成功案例 */
.material-handling-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff3e0 100%);
}

.material-handling-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.material-handling-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.material-handling-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.material-handling-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.material-handling-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.material-handling-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.material-handling-success .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.automotive-plant .story-category {
  background: linear-gradient(135deg, #FF9800, #FFB74D);
}

.story-card.mining-operation .story-category {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.material-handling-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.material-handling-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.material-handling-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.material-handling-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #FF9800;
  margin-bottom: 5px;
}

.material-handling-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 物料搬运技术规格 */
.material-handling-specs {
  background: white;
}

.material-handling-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 物料搬运CTA增强 */
.material-handling-cta {
  background: linear-gradient(135deg, #FF9800 0%, #FFB74D 50%, #FFCC02 100%);
  position: relative;
  overflow: hidden;
}

.material-handling-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.material-handling-advantages {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.material-handling-advantages .advantage-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.material-handling-advantages .advantage-item .advantage-icon {
  font-size: 1.2rem;
}

/* 物料搬运行业响应式设计 */
@media (max-width: 1024px) {
  .material-handling-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .material-handling-industry .solutions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20px !important;
  }


}

@media (max-width: 768px) {
  .material-handling-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .material-handling-industry .challenge-item {
    padding: 25px 20px;
  }

  .material-handling-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .material-handling-systems .systems-grid,
  .material-flow-process .process-flow,
  .material-handling-success .success-stories {
    grid-template-columns: 1fr;
  }

  .material-handling-systems .system-card,
  .material-flow-process .process-step,
  .material-handling-success .story-card {
    padding: 25px;
  }

  .material-handling-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .material-handling-success .story-results {
    justify-content: space-around;
  }

  .material-handling-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .material-handling-advantages {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .material-flow-process .process-flow {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .material-handling-systems,
  .material-flow-process,
  .material-handling-success,
  .material-handling-specs {
    padding: 60px 0;
  }

  .material-handling-systems .systems-grid,
  .material-flow-process .process-flow,
  .material-handling-success .success-stories {
    gap: 20px;
  }

  .material-handling-systems .system-card,
  .material-flow-process .process-step,
  .material-handling-success .story-card {
    padding: 20px;
  }

  .material-handling-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .material-handling-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .material-handling-success .result-item {
    min-width: 80px;
  }

  .material-handling-success .result-number {
    font-size: 1.5rem;
  }

  .material-flow-process .process-flow {
    grid-template-columns: 1fr;
  }

  .material-flow-process .process-step {
    padding: 25px 20px;
  }
}

/* 烟草行业页面专用样式 */
.tobacco-industry .hero-features .feature-item {
  background: rgba(141,110,99,0.1);
  border: 1px solid rgba(141,110,99,0.3);
}

/* 烟草行业挑战部分 - 单行布局 */
.tobacco-industry .challenges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.tobacco-industry .challenge-item {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid #8D6E63;
}

.tobacco-industry .challenge-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.tobacco-industry .challenge-item h3 {
  color: #1A365D;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.3;
}

.tobacco-industry .challenge-item p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 烟草行业产品解决方案优化 - 单排显示 */
.tobacco-industry .solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 20px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* 烟草行业解决方案卡片布局优化 */
.tobacco-industry .solution-category {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.tobacco-industry .category-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.tobacco-industry .product-features {
  flex: 1 !important;
}

.tobacco-industry .applications {
  margin-top: auto !important;
}

/* 烟草加工系统 */
.tobacco-processing-systems {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #efebe9 100%);
}

.tobacco-processing-systems h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.tobacco-processing-systems .systems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.tobacco-processing-systems .system-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tobacco-processing-systems .system-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #8D6E63, #A1887F);
}

.tobacco-processing-systems .system-card.secondary-processing::before {
  background: linear-gradient(90deg, #FF8F00, #FFB74D);
}

.tobacco-processing-systems .system-card.cigarette-making::before {
  background: linear-gradient(90deg, #D32F2F, #F44336);
}

.tobacco-processing-systems .system-card.packaging-systems::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.tobacco-processing-systems .system-card.filter-production::before {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.tobacco-processing-systems .system-card.material-handling::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.tobacco-processing-systems .system-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.tobacco-processing-systems .system-icon {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.tobacco-processing-systems .system-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin-bottom: 15px;
  text-align: center;
}

.tobacco-processing-systems .system-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.tobacco-processing-systems .system-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tobacco-processing-systems .system-features li {
  padding: 6px 0;
  padding-left: 20px;
  position: relative;
  color: #555;
  font-size: 0.9rem;
}

.tobacco-processing-systems .system-features li::before {
  content: '🌿';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.tobacco-processing-systems .system-card.secondary-processing .system-features li::before {
  content: '⚙️';
}

.tobacco-processing-systems .system-card.cigarette-making .system-features li::before {
  content: '🚬';
}

.tobacco-processing-systems .system-card.packaging-systems .system-features li::before {
  content: '📦';
}

.tobacco-processing-systems .system-card.filter-production .system-features li::before {
  content: '🔵';
}

.tobacco-processing-systems .system-card.material-handling .system-features li::before {
  content: '🔄';
}

/* 产品解决方案增强 */
.tobacco-industry .solution-category.high-speed-precision .category-header {
  background: linear-gradient(135deg, #D32F2F, #F44336);
}

.tobacco-industry .solution-category.food-grade-hygienic .category-header {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.tobacco-industry .solution-category.dust-resistant .category-header {
  background: linear-gradient(135deg, #FF8F00, #FFB74D);
}

.tobacco-industry .solution-category.regulatory-compliant .category-header {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.speed-rating, .hygiene-rating, .protection-rating, .compliance-rating {
  background: rgba(255,255,255,0.2);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 烟草生产流程 */
.tobacco-production-process {
  padding: 80px 0;
  background: white;
}

.tobacco-production-process h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.tobacco-production-process .process-flow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.tobacco-production-process .process-step {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  position: relative;
  transition: transform 0.3s ease;
}

.tobacco-production-process .process-step:hover {
  transform: translateY(-5px);
}

.tobacco-production-process .step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: #8D6E63;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.tobacco-production-process .process-step.blending-cutting .step-number {
  background: #FF8F00;
}

.tobacco-production-process .process-step.cigarette-making .step-number {
  background: #D32F2F;
}

.tobacco-production-process .process-step.packaging .step-number {
  background: #4CAF50;
}

.tobacco-production-process .step-icon {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.tobacco-production-process .process-step h3 {
  color: #1A365D;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.tobacco-production-process .process-step p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.tobacco-production-process .step-requirements {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tobacco-production-process .step-requirements span {
  background: rgba(141,110,99,0.1);
  color: #8D6E63;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.tobacco-production-process .process-step.blending-cutting .step-requirements span {
  background: rgba(255,143,0,0.1);
  color: #FF8F00;
}

.tobacco-production-process .process-step.cigarette-making .step-requirements span {
  background: rgba(211,47,47,0.1);
  color: #D32F2F;
}

.tobacco-production-process .process-step.packaging .step-requirements span {
  background: rgba(76,175,80,0.1);
  color: #4CAF50;
}

/* 烟草成功案例 */
.tobacco-success {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #efebe9 100%);
}

.tobacco-success h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 50px;
  text-align: center;
}

.tobacco-success .success-stories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
}

.tobacco-success .story-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tobacco-success .story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.tobacco-success .story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.tobacco-success .story-header h3 {
  color: #1A365D;
  font-size: 1.3rem;
  margin: 0;
}

.tobacco-success .story-category {
  background: linear-gradient(135deg, #D32F2F, #F44336);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.story-card.filter-producer .story-category {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.story-card.packaging-plant .story-category {
  background: linear-gradient(135deg, #FF8F00, #FFB74D);
}

.tobacco-success .story-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.tobacco-success .story-results {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  flex-wrap: wrap;
}

.tobacco-success .result-item {
  text-align: center;
  flex: 1;
  min-width: 100px;
}

.tobacco-success .result-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #8D6E63;
  margin-bottom: 5px;
}

.tobacco-success .result-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

/* 烟草技术规格 */
.tobacco-specs {
  background: white;
}

.tobacco-specs .specs-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 烟草CTA增强 */
.tobacco-cta {
  background: linear-gradient(135deg, #8D6E63 0%, #A1887F 50%, #BCAAA4 100%);
  position: relative;
  overflow: hidden;
}

.tobacco-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.tobacco-excellence {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.tobacco-excellence .excellence-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255,255,255,0.9);
  font-size: 0.95rem;
}

.tobacco-excellence .excellence-item .excellence-icon {
  font-size: 1.2rem;
}

/* 烟草行业响应式设计 */
@media (max-width: 1024px) {
  .tobacco-industry .challenges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .tobacco-industry .solutions-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 25px !important;
    max-width: 900px !important;
  }
}

@media (max-width: 768px) {
  .tobacco-industry .challenges-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .tobacco-industry .challenge-item {
    padding: 25px 20px;
  }

  .tobacco-industry .solutions-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .tobacco-processing-systems .systems-grid,
  .tobacco-production-process .process-flow,
  .tobacco-success .success-stories {
    grid-template-columns: 1fr;
  }

  .tobacco-processing-systems .system-card,
  .tobacco-production-process .process-step,
  .tobacco-success .story-card {
    padding: 25px;
  }

  .tobacco-success .story-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .tobacco-success .story-results {
    justify-content: space-around;
  }

  .tobacco-specs .specs-overview {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .tobacco-excellence {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .tobacco-production-process .process-flow {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .tobacco-processing-systems,
  .tobacco-production-process,
  .tobacco-success,
  .tobacco-specs {
    padding: 60px 0;
  }

  .tobacco-processing-systems .systems-grid,
  .tobacco-production-process .process-flow,
  .tobacco-success .success-stories {
    gap: 20px;
  }

  .tobacco-processing-systems .system-card,
  .tobacco-production-process .process-step,
  .tobacco-success .story-card {
    padding: 20px;
  }

  .tobacco-industry .hero-features {
    flex-direction: column;
    align-items: center;
  }

  .tobacco-industry .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .tobacco-success .result-item {
    min-width: 80px;
  }

  .tobacco-success .result-number {
    font-size: 1.5rem;
  }

  .tobacco-production-process .process-flow {
    grid-template-columns: 1fr;
  }

  .tobacco-production-process .process-step {
    padding: 25px 20px;
  }
}

/* Enhanced Homepage Styles */

/* Hero Section Enhancements */
.hero-text .hero-slogan {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin: 0 0 15px 0;
  color: #FFD700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-text .hero-description {
  font-size: 1rem;
  font-weight: 300;
  line-height: 1.5;
  margin: 0 0 25px 0;
  opacity: 0.95;
  color: rgba(255,255,255,0.9);
  max-width: 480px;
}

/* Hero CTA Buttons */
.hero-cta-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;
  border: 2px solid transparent;
  cursor: pointer;
}

/* 重复的按钮样式已移除，使用上面的统一定义 */

.btn-outline {
  background: transparent;
  color: white;
  border-color: rgba(255,255,255,0.8);
}

.btn-outline:hover {
  background: rgba(255,255,255,0.1);
  border-color: white;
  transform: translateY(-2px);
}

.btn-secondary {
  background: rgba(255,255,255,0.15);
  color: white;
  border-color: rgba(255,255,255,0.3);
}

.btn-secondary:hover {
  background: rgba(255,255,255,0.25);
  transform: translateY(-2px);
}

.btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
}

/* Hero Credentials */
.hero-credentials {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.credential-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255,255,255,0.1);
  padding: 8px 15px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.credential-icon {
  font-size: 1.2rem;
}

.credential-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255,255,255,0.95);
}

/* Enhanced Navbar */
.navbar-logo .logo-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.navbar-logo .logo-link:hover {
  text-decoration: none;
  color: inherit;
}

.navbar-logo .logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
}

.logo-icon {
  font-size: 1.8rem;
  background: var(--gradient-orange);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(255,106,0,0.3));
  transition: all 0.3s ease;
}

.logo-container:hover .logo-icon {
  transform: rotate(180deg) scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(255,106,0,0.5));
}

.logo-text {
  background: linear-gradient(135deg, #1A365D 0%, #2E5C8A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.logo-container:hover .logo-text {
  background: linear-gradient(135deg, #FF6A00 0%, #FF8533 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-text {
  font-weight: 700;
  font-size: 1.1rem;
  color: #1A365D;
}

.nav-cta-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* About Section - 减少底部空白 */
.about-section {
  padding: 80px 0 40px; /* 减少底部padding */
  background: white;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 60px;
  flex-wrap: wrap;
}

.about-text {
  flex: 1 1 400px;
}

.about-text h2 {
  font-size: 2.5rem;
  color: #1A365D;
  margin-bottom: 10px;
}

.about-text h3 {
  font-size: 1.5rem;
  color: #FF6A00;
  margin-bottom: 20px;
  font-weight: 600;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666;
  margin-bottom: 30px;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  color: #FF6A00;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.about-image {
  flex: 1 1 300px;
  display: flex;
  justify-content: center;
}

.image-placeholder {
  width: 100%;
  max-width: 400px;
  height: 300px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 10px;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 1.2rem;
  color: #666;
  font-weight: 500;
}

/* Products Section - 减少底部空白 */
.products-section {
  padding: 60px 0 30px; /* 减少底部padding */
  background: #f8f9fa;
}

.products-section h2 {
  font-size: 2.2rem;
  color: #1A365D;
  text-align: center;
  margin-bottom: 15px;
}

.section-intro {
  text-align: center;
  font-size: 1rem;
  color: #666;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
}

.product-category {
  background: white;
  padding: 20px 18px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.product-icon {
  font-size: 1.8rem;
  margin-bottom: 12px;
  display: block;
  color: #FF6A00;
}

.product-category h3 {
  font-size: 1.1rem;
  color: #1A365D;
  margin-bottom: 10px;
  line-height: 1.3;
}

.product-category p {
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
  font-size: 0.85rem;
  flex: 1;
}

.product-features {
  list-style: none;
  padding: 0;
  margin: 12px 0 0 0;
  text-align: left;
}

.product-features li {
  padding: 3px 0;
  padding-left: 16px;
  position: relative;
  color: #555;
  font-size: 0.8rem;
  line-height: 1.4;
}

.product-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #FF6A00;
  font-size: 0.9rem;
  font-weight: bold;
}

/* Products Section Responsive */
@media (max-width: 1024px) {
  .products-grid {
    grid-template-columns: 1fr;
    max-width: 600px;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .products-section {
    padding: 50px 0;
  }

  .products-section h2 {
    font-size: 1.8rem;
  }

  .section-intro {
    font-size: 0.9rem;
    margin-bottom: 30px;
  }

  .product-category {
    padding: 18px 14px;
  }

  .product-icon {
    font-size: 1.6rem;
    margin-bottom: 10px;
  }

  .product-category h3 {
    font-size: 1rem;
  }

  .product-category p {
    font-size: 0.8rem;
  }

  .product-features li {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .products-section {
    padding: 40px 0;
  }

  .products-section h2 {
    font-size: 1.6rem;
  }

  .product-category {
    padding: 16px 10px;
  }

  .product-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  .product-category h3 {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }

  .product-category p {
    font-size: 0.75rem;
    margin-bottom: 10px;
  }

  .product-features {
    margin: 12px 0 0 0;
  }

  .product-features li {
    padding: 3px 0;
    font-size: 0.75rem;
    padding-left: 15px;
  }
}

/* Main Footer - 紧凑布局，使用CSS变量 */
.main-footer {
  background: var(--gradient-primary);
  color: white;
  padding: 20px 0 0; /* 进一步减少顶部padding */
  margin-bottom: 0; /* 确保没有底部margin */
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 20px; /* 最小化网格间距 */
  margin-bottom: 10px; /* 最小化底部margin */
}

.footer-section h3 {
  font-size: 1.4rem;
  margin-bottom: 8px; /* 大幅减少标题下间距 */
  color: #FFD700;
  font-weight: 600;
}

.footer-section h4 {
  font-size: 1.1rem;
  margin-bottom: 10px; /* 大幅减少标题下间距 */
  color: white;
  font-weight: 600;
}

.footer-section p {
  line-height: 1.5; /* 减少行高 */
  color: rgba(255,255,255,0.8);
  margin-bottom: 15px; /* 大幅减少段落下间距 */
  font-size: 0.9rem;
}

/* 联系信息网格 - 减少间距 */
.contact-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px; /* 减少网格间距 */
  margin-top: 15px; /* 减少顶部间距 */
}

.contact-card {
  display: flex;
  align-items: flex-start;
  gap: 10px; /* 减少内部间距 */
  padding: 10px; /* 减少内边距 */
  background: rgba(255,255,255,0.05);
  border-radius: 8px;
  border-left: 3px solid #FF6A00;
  transition: all 0.3s ease;
}

.contact-card:hover {
  background: rgba(255,255,255,0.1);
  transform: translateY(-2px);
}

.contact-icon {
  font-size: 1.2rem;
  margin-top: 2px;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contact-label {
  font-size: 0.75rem;
  color: rgba(255,255,255,0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  font-size: 0.9rem;
  color: rgba(255,255,255,0.9);
  font-weight: 500;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 4px; /* 减少列表项间距 */
}

.footer-section ul li a {
  color: rgba(255,255,255,0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #FFD700;
}

/* 改进的邮件订阅表单 - 减少间距 */
.newsletter-form {
  display: flex;
  gap: 8px;
  margin-bottom: 12px; /* 大幅减少底部间距 */
  background: rgba(255,255,255,0.05);
  padding: 8px;
  border-radius: 8px;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  background: rgba(255,255,255,0.9);
  color: #333;
}

.newsletter-form input::placeholder {
  color: #666;
}

.newsletter-form button {
  background: #FF6A00;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-form button:hover {
  background: #E55A00;
  transform: translateY(-1px);
}

/* 改进的社交链接 - 减少间距 */
.social-links {
  display: flex;
  gap: 8px; /* 减少社交链接间距 */
  flex-wrap: wrap;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255,255,255,0.8);
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 6px;
  background: rgba(255,255,255,0.05);
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.social-link:hover {
  color: white;
  background: rgba(255,255,255,0.1);
  transform: translateY(-2px);
}

.social-link.linkedin:hover {
  background: #0077B5;
}

.social-link.facebook:hover {
  background: #1877F2;
}

.social-link.youtube:hover {
  background: #FF0000;
}

.social-icon {
  font-size: 1.1rem;
}

/* Certifications Area - 最小间距 */
.footer-certifications {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: 15px 0 10px; /* 最小化上下margin */
  padding: 15px; /* 最小化内边距 */
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  flex-wrap: wrap;
  border: 1px solid rgba(255,255,255,0.1);
}

.cert-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
  padding: 20px 25px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 700;
  color: #1A365D;
  transition: all 0.3s ease;
  border: 2px solid #FF6A00;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  position: relative;
  min-width: 120px;
  text-align: center;
}

.cert-item::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255,106,0,0.05) 0%, rgba(255,106,0,0.1) 100%);
  border-radius: 13px;
  z-index: -1;
}

.cert-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.cert-icon {
  font-size: 1.8rem;
  margin-bottom: 5px;
  transition: all 0.3s ease;
}

.cert-item:hover .cert-icon {
  transform: scale(1.05);
}

/* CTA按钮 - 最小间距 */
.footer-cta {
  text-align: center;
  margin: 10px 0 8px; /* 最小化上下margin */
}

.footer-cta-btn {
  display: inline-block;
  background: linear-gradient(135deg, #FF6A00 0%, #E55A00 100%);
  color: white;
  padding: 15px 40px;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 106, 0, 0.3);
}

.footer-cta-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 106, 0, 0.4);
  background: linear-gradient(135deg, #E55A00 0%, #CC5100 100%);
}

.footer-bottom {
  border-top: 1px solid rgba(255,255,255,0.2);
  padding-top: 15px; /* 最小化顶部padding */
  padding-bottom: 0; /* 移除底部padding */
  text-align: center;
  color: rgba(255,255,255,0.7);
  font-size: 0.85rem;
}

.footer-bottom a {
  color: rgba(255,255,255,0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom a:hover {
  color: #FFD700;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    min-height: 400px;
    padding: 0 0 40px 0;
  }

  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-text .hero-slogan {
    font-size: 1.2rem;
  }

  .hero-text .hero-description {
    font-size: 0.9rem;
  }

  .hero-cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-cta-buttons .btn {
    width: 100%;
    max-width: 250px;
  }

  .hero-credentials {
    justify-content: center;
    gap: 10px;
  }

  .credential-item {
    padding: 6px 12px;
  }

  .credential-text {
    font-size: 0.8rem;
  }

  .about-content {
    flex-direction: column;
    text-align: center;
  }

  .about-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  /* 页脚响应式设计 - 移动端紧凑布局 */
  .main-footer {
    padding: 15px 0 0; /* 移动端进一步减少padding */
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 15px; /* 进一步减少移动端gap */
    text-align: center;
    margin-bottom: 8px; /* 进一步减少底部margin */
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .contact-card {
    padding: 12px;
    flex-direction: row;
    justify-content: center;
  }

  .footer-certifications {
    flex-direction: column;
    gap: 10px;
    padding: 10px; /* 最小化移动端padding */
    margin: 12px 0 8px; /* 最小化移动端margin */
  }

  .cert-item {
    justify-content: center;
    padding: 10px 16px;
  }

  .newsletter-form {
    flex-direction: column;
    gap: 12px;
  }

  .newsletter-form input,
  .newsletter-form button {
    width: 100%;
  }

  .social-links {
    justify-content: center;
    gap: 10px;
  }

  .footer-cta {
    margin: 8px 0 5px; /* 最小化移动端CTA按钮区域margin */
  }

  .footer-bottom {
    padding-top: 10px; /* 最小化移动端底部padding */
  }

  .footer-cta-btn {
    padding: 12px 30px;
    font-size: 1rem;
  }
}

  .newsletter-form {
    flex-direction: column;
  }

  .social-links {
    justify-content: center;
  }

  .nav-cta-buttons {
    flex-direction: column;
    gap: 5px;
  }

  .nav-cta-buttons .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 350px;
    padding: 0 0 30px 0;
  }

  .hero-content {
    padding-top: 20px;
    gap: 20px;
  }

  .hero-text h1 {
    font-size: 1.6rem;
  }

  .hero-text .hero-slogan {
    font-size: 1rem;
  }

  .about-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: 15px;
  }

  .stat-number {
    font-size: 2rem;
  }

  .product-category {
    padding: 30px 20px;
  }

  .certifications {
    justify-content: center;
  }

  .contact-info {
    text-align: left;
  }
}

/* Markets Section - 减少底部空白 */
.markets-section {
  padding: 80px 0 20px; /* 大幅减少底部padding */
  background: white;
}

.markets-section h2 {
  font-size: 2.5rem;
  color: #1A365D;
  text-align: center;
  margin-bottom: 20px;
}

.market-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin: 50px 0;
}

.market-stat {
  text-align: center;
  padding: 30px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.market-stat:hover {
  transform: translateY(-5px);
}

.market-stat .stat-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 15px;
}

.market-stat .stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  color: #FF6A00;
  margin-bottom: 10px;
}

.market-stat .stat-label {
  font-size: 1rem;
  color: #666;
  font-weight: 500;
}

.market-regions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin: 50px 0;
}

.region-item {
  padding: 25px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e0e0e0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.region-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.region-item h3 {
  font-size: 1.3rem;
  color: #1A365D;
  margin-bottom: 10px;
}

.region-item p {
  color: #666;
  font-size: 0.95rem;
}

.market-cta {
  text-align: center;
  margin-top: 60px;
  padding: 50px 30px;
  background: linear-gradient(135deg, #1A365D 0%, #2E5C8A 100%);
  border-radius: 16px;
  color: white;
}

.market-cta h3 {
  font-size: 2rem;
  margin-bottom: 15px;
  color: #FFD700;
}

.market-cta p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: rgba(255,255,255,0.9);
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Markets responsive design */
@media (max-width: 768px) {
  .market-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .market-regions {
    grid-template-columns: 1fr;
  }

  .market-cta {
    padding: 40px 20px;
  }

  .market-cta h3 {
    font-size: 1.6rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .market-stats {
    grid-template-columns: 1fr;
  }

  .market-stat {
    padding: 25px 15px;
  }

  .market-stat .stat-number {
    font-size: 2rem;
  }
}

/* Enhanced Homepage Sections */

/* Core Advantages Section */
.core-advantages {
  padding: 70px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.core-advantages h2 {
  font-size: 2.5rem;
  color: #1A365D;
  text-align: center;
  margin-bottom: 15px;
  font-weight: 700;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin: 50px 0;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.advantage-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);
}

.advantage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF6A00, #E55A00);
}

.advantage-card.pricing::before {
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.advantage-card.supply::before {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.advantage-card.technical::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.advantage-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0,0,0,0.15);
}

.advantage-top {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.advantage-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  flex-shrink: 0;
  line-height: 1;
}

.advantage-content {
  flex: 1;
}

.advantage-card h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin: 0 0 8px 0;
  font-weight: 600;
  line-height: 1.2;
}

.advantage-highlight {
  background: linear-gradient(135deg, #FF6A00, #E55A00);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-block;
}

.advantage-card.pricing .advantage-highlight {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.advantage-card.supply .advantage-highlight {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.advantage-card.technical .advantage-highlight {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

.advantage-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 1rem;
}

.advantage-points {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.point {
  color: #555;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.point::before {
  content: '';
  width: 4px;
  height: 4px;
  background: #FF6A00;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.advantages-cta {
  text-align: center;
  padding: 40px 30px;
  background: linear-gradient(135deg, #1A365D 0%, #2E5C8A 100%);
  border-radius: 16px;
  color: white;
  position: relative;
  overflow: hidden;
  margin-top: 50px;
}

.advantages-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%);
  pointer-events: none;
}

.advantages-cta h3 {
  font-size: 1.8rem;
  margin-bottom: 15px;
  color: #FFD700;
  position: relative;
  z-index: 1;
}

.advantages-cta p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  color: rgba(255,255,255,0.9);
  position: relative;
  z-index: 1;
}

/* Certifications Showcase */
.certifications-showcase {
  padding: 80px 0;
  background: white;
}

.certifications-showcase h2 {
  font-size: 2.5rem;
  color: #1A365D;
  text-align: center;
  margin-bottom: 20px;
}

.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin: 50px 0;
}

.cert-item {
  background: #f8f9fa;
  padding: 30px 20px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s ease;
  border: 2px solid transparent;
}

.cert-item:hover {
  transform: translateY(-5px);
  border-color: #FF6A00;
}

.cert-logo {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.cert-item h3 {
  font-size: 1.3rem;
  color: #1A365D;
  margin-bottom: 10px;
}

.cert-item p {
  color: #666;
  margin-bottom: 10px;
}

.cert-status {
  background: #4CAF50;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.quality-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin: 40px 0;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 12px;
}

.metric-item {
  text-align: center;
}

.metric-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #FF6A00;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.quality-commitment {
  text-align: center;
  margin-top: 40px;
}

.quality-commitment h3 {
  font-size: 1.5rem;
  color: #1A365D;
  margin-bottom: 15px;
}

.quality-commitment p {
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Success Stories Section */
.success-stories {
  padding: 70px 0;
  background: #f8f9fa;
}

.success-stories h2 {
  font-size: 2.5rem;
  color: #1A365D;
  text-align: center;
  margin-bottom: 20px;
}

.stories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  margin: 40px 0;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.story-card {
  background: white;
  border-radius: 12px;
  padding: 18px;
  box-shadow: 0 6px 24px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.story-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 32px rgba(0,0,0,0.12);
}

.story-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 8px;
}

.story-header h3 {
  color: #1A365D;
  font-size: 1.05rem;
  margin: 0;
  line-height: 1.2;
  flex: 1;
  font-weight: 600;
}

.industry-tag {
  background: linear-gradient(135deg, #FF6A00, #E55A00);
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;
}

.story-card.food .industry-tag {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.story-card.manufacturing .industry-tag {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.story-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.challenge-solution {
  margin-bottom: 12px;
}

.detail-item {
  margin-bottom: 6px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 600;
  color: #1A365D;
  font-size: 0.8rem;
}

.detail-text {
  color: #666;
  line-height: 1.4;
  font-size: 0.85rem;
}

.story-results {
  margin-top: auto;
  padding-top: 12px;
}

.result-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 6px;
  margin-bottom: 0;
}

.result-row:last-child {
  margin-bottom: 0;
}

.result-item {
  text-align: center;
  padding: 8px 4px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  min-height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.result-item.single {
  grid-column: span 1;
  margin: 0;
}

.result-number {
  display: block;
  font-size: 1.1rem;
  font-weight: bold;
  color: #FF6A00;
  margin-bottom: 2px;
  line-height: 1;
}

.result-label {
  font-size: 0.65rem;
  color: #666;
  font-weight: 500;
  line-height: 1.1;
}

.success-cta {
  text-align: center;
  margin-top: 60px;
  padding: 50px 30px;
  background: linear-gradient(135deg, #1A365D 0%, #2E5C8A 100%);
  border-radius: 16px;
  color: white;
}

.success-cta h3 {
  font-size: 2rem;
  margin-bottom: 15px;
  color: #FFD700;
}

.success-cta p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: rgba(255,255,255,0.9);
}

/* Enhanced Industries Section */
.industries-grid-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin: 50px 0;
}

.industry-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.industry-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.industry-card .industry-icon {
  margin-bottom: 20px;
}

.industry-card .industry-icon img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.industry-card h3 {
  font-size: 1.2rem;
  color: #1A365D;
  margin-bottom: 10px;
}

.industry-card p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 15px;
}

.industry-link {
  color: #FF6A00;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.industry-link:hover {
  color: #E55A00;
}

.industries-cta {
  text-align: center;
  margin-top: 40px;
}

.industries-cta p {
  color: #666;
  margin-bottom: 20px;
}

/* Technical Capabilities Section */
.technical-capabilities {
  padding: 80px 0;
  background: #f8f9fa;
}

.technical-capabilities h2 {
  font-size: 2.5rem;
  color: #1A365D;
  text-align: center;
  margin-bottom: 20px;
}

.tech-capabilities-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  margin: 50px 0;
}

.capability-card {
  background: white;
  border-radius: 12px;
  padding: 25px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-top: 4px solid transparent;
}

.capability-card:nth-child(1) {
  border-top-color: #FF6A00;
}

.capability-card:nth-child(2) {
  border-top-color: #28A745;
}

.capability-card:nth-child(3) {
  border-top-color: #007BFF;
}

.capability-card:nth-child(4) {
  border-top-color: #6F42C1;
}

.capability-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.capability-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.capability-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 106, 0, 0.1);
}

.capability-card:nth-child(2) .capability-icon {
  background: rgba(40, 167, 69, 0.1);
}

.capability-card:nth-child(3) .capability-icon {
  background: rgba(0, 123, 255, 0.1);
}

.capability-card:nth-child(4) .capability-icon {
  background: rgba(111, 66, 193, 0.1);
}

.capability-header h3 {
  font-size: 1.4rem;
  color: #1A365D;
  margin: 0;
  font-weight: 600;
}

.capability-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 0;
  font-size: 0.95rem;
}

/* Technical Capabilities 响应式 */
@media (max-width: 1200px) {
  .tech-capabilities-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .capability-card {
    padding: 20px 15px;
  }

  .capability-header h3 {
    font-size: 1.2rem;
  }

  .capability-icon {
    width: 50px;
    height: 50px;
    font-size: 2rem;
    margin-right: 12px;
  }

  .capability-content p {
    font-size: 0.9rem;
  }
}

@media (max-width: 900px) {
  .tech-capabilities-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .capability-card {
    padding: 20px 15px;
  }
}

@media (max-width: 600px) {
  .technical-capabilities {
    padding: 60px 0;
  }

  .technical-capabilities h2 {
    font-size: 2rem;
  }

  .tech-capabilities-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    margin: 40px 0;
  }

  .capability-card {
    padding: 20px 15px;
  }

  .capability-header {
    flex-direction: column;
    text-align: center;
    margin-bottom: 15px;
  }

  .capability-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .capability-header h3 {
    font-size: 1.1rem;
  }

  .capability-content p {
    font-size: 0.9rem;
    margin-bottom: 0;
  }
}

.tech-innovation {
  margin-top: 50px;
  text-align: center;
}

.tech-innovation h3 {
  font-size: 1.8rem;
  color: #1A365D;
  margin-bottom: 30px;
}

.innovation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.innovation-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.innovation-title {
  display: block;
  font-weight: 600;
  color: #1A365D;
  margin-bottom: 8px;
}

.innovation-desc {
  font-size: 0.9rem;
  color: #666;
}

/* Enhanced Responsive Design for New Sections */
@media (max-width: 1024px) and (min-width: 769px) {
  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .advantage-card:last-child {
    grid-column: 1 / -1;
    max-width: 50%;
    margin: 0 auto;
  }

  .stories-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 600px;
  }

  .story-card {
    padding: 16px;
  }

  .story-header {
    margin-bottom: 10px;
  }

  .challenge-solution {
    margin-bottom: 10px;
  }

  .story-results {
    padding-top: 10px;
  }

  .result-row {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .result-item:nth-child(3) {
    grid-column: 1 / -1;
    max-width: 50%;
    margin: 6px auto 0;
  }
}

@media (max-width: 768px) {
  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 25px;
    margin: 40px 0;
  }

  .advantage-card {
    padding: 25px 20px;
  }

  .advantage-top {
    margin-bottom: 15px;
  }

  .advantage-icon {
    font-size: 2rem;
    margin-right: 12px;
  }

  .advantage-card h3 {
    font-size: 1.2rem;
  }

  .advantage-highlight {
    font-size: 0.8rem;
    padding: 3px 10px;
  }

  .advantage-card p {
    font-size: 0.95rem;
    margin-bottom: 15px;
  }

  .point {
    font-size: 0.85rem;
  }

  .advantages-cta {
    padding: 30px 20px;
  }

  .advantages-cta h3 {
    font-size: 1.5rem;
  }

  .advantages-cta p {
    font-size: 1rem;
  }

  .certifications-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .quality-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .stories-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .story-card {
    padding: 16px;
  }

  .story-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    margin-bottom: 10px;
  }

  .story-header h3 {
    font-size: 0.95rem;
  }

  .industry-tag {
    align-self: flex-start;
    font-size: 0.65rem;
    padding: 2px 6px;
  }

  .challenge-solution {
    margin-bottom: 12px;
  }

  .detail-item {
    margin-bottom: 6px;
  }

  .detail-label {
    font-size: 0.75rem;
  }

  .detail-text {
    font-size: 0.8rem;
  }

  .story-results {
    padding-top: 12px;
  }

  .result-row {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 6px;
  }

  .result-item {
    padding: 8px 4px;
  }

  .result-item:nth-child(3) {
    grid-column: 1 / -1;
    max-width: 50%;
    margin: 6px auto 0;
  }

  .result-number {
    font-size: 1.1rem;
  }

  .result-label {
    font-size: 0.65rem;
  }

  .success-cta {
    padding: 40px 20px;
  }

  .success-cta h3 {
    font-size: 1.6rem;
  }

  .industries-grid-enhanced {
    grid-template-columns: 1fr;
  }

  .industry-card {
    padding: 25px;
  }

  .tech-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .tech-capabilities {
    grid-template-columns: 1fr;
  }

  .capability-item {
    padding: 25px 15px;
  }

  .innovation-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .core-advantages,
  .certifications-showcase,
  .success-stories,
  .technical-capabilities {
    padding: 50px 0;
  }

  .core-advantages {
    padding: 50px 0;
  }

  .core-advantages h2 {
    font-size: 2rem;
  }

  .advantages-grid {
    margin: 30px 0;
    gap: 20px;
  }

  .advantage-card {
    padding: 20px 15px;
  }

  .advantage-top {
    margin-bottom: 12px;
  }

  .advantage-icon {
    font-size: 1.8rem;
    margin-right: 10px;
  }

  .advantage-card h3 {
    font-size: 1.1rem;
  }

  .advantage-highlight {
    font-size: 0.75rem;
    padding: 2px 8px;
  }

  .advantage-card p {
    font-size: 0.9rem;
    margin-bottom: 12px;
  }

  .point {
    font-size: 0.8rem;
  }

  .advantages-cta {
    padding: 25px 15px;
  }

  .advantages-cta h3 {
    font-size: 1.4rem;
  }

  .advantages-cta p {
    font-size: 0.95rem;
    margin-bottom: 25px;
  }

  .story-card {
    padding: 14px;
  }

  .story-header {
    margin-bottom: 8px;
    gap: 4px;
  }

  .story-header h3 {
    font-size: 0.9rem;
  }

  .industry-tag {
    font-size: 0.6rem;
    padding: 2px 6px;
  }

  .challenge-solution {
    margin-bottom: 8px;
  }

  .detail-item {
    margin-bottom: 5px;
    gap: 1px;
  }

  .detail-label {
    font-size: 0.7rem;
  }

  .detail-text {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .story-results {
    padding-top: 10px;
  }

  .result-row {
    grid-template-columns: 1fr;
    gap: 4px;
    margin-bottom: 5px;
  }

  .result-item {
    padding: 6px 3px;
  }

  .result-item:nth-child(3) {
    grid-column: auto;
    max-width: none;
    margin: 0;
  }

  .result-number {
    font-size: 1rem;
    margin-bottom: 1px;
  }

  .result-label {
    font-size: 0.6rem;
  }

  .industry-card {
    padding: 20px;
  }

  .certifications-grid {
    grid-template-columns: 1fr;
  }

  .quality-metrics {
    grid-template-columns: 1fr;
  }

  .metric-item {
    padding: 15px;
  }

  .metric-number {
    font-size: 1.8rem;
  }

  .story-results {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .result-item {
    min-width: auto;
  }

  .tech-stats {
    grid-template-columns: 1fr;
  }

  .tech-number {
    font-size: 2rem;
  }

  .advantages-cta .cta-buttons,
  .success-cta .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 250px;
  }
}

/* {{ AURA-X: Add - 现代化图片性能优化样式. Source: context7-mcp on 'WP Rocket Performance' }} */

/* 全局图片优化 */
img {
  /* 现代化图片渲染优化 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;

  /* 防止布局偏移 */
  height: auto;

  /* 解码优化 */
  decoding: async;
}

/* 懒加载动画 */
img[data-src] {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

img.loaded,
img[src]:not([data-src]) {
  background: none;
  animation: none;
}

/* 响应式图片优化 */
img[srcset] {
  sizes: (max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw;
}

/* 关键图片预加载 */
.hero-image img,
.logo img {
  loading: eager;
  fetchpriority: high;
}

/* 内容可见性优化 */
[data-wpr-lazyrender] {
  content-visibility: auto;
  contain-intrinsic-size: 0 400px;
}

/* 现代化滚动优化 */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* GPU加速优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 字体显示优化 */
@font-face {
  font-family: 'SF Pro Display';
  font-display: swap;
  font-weight: 400 700;
}

/* 预连接优化 */
link[rel="preconnect"] {
  crossorigin: anonymous;
}