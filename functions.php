<?php
// 主题功能设置

// 引入联系方式小方框组件
require_once get_template_directory() . '/includes/contact-box.php';

// 注册和加载样式和脚本
function theme_asset_version($relative_path) {
    $file = get_template_directory() . $relative_path;
    if (file_exists($file)) {
        return (string) filemtime($file);
    }
    return '1.0.0';
}

function my_theme_scripts() {
    // {{ AURA-X: Modify - 关键CSS内联优化，提升首屏渲染速度. Source: context7-mcp on 'WP Rocket Performance' }}
    // 关键CSS内联处理
    $critical_css = get_template_directory() . '/assets/css/critical.css';
    if (file_exists($critical_css)) {
        wp_add_inline_style('wp-block-library', file_get_contents($critical_css));
    }

    // 主样式表 - 使用 preload 策略
    wp_enqueue_style('main-style', get_template_directory_uri() . '/assets/css/style.css', array(), theme_asset_version('/assets/css/style.css'));
    wp_style_add_data('main-style', 'preload', true);

    // 联系方式样式 - 条件加载
    if (is_page_template('templates/contact.php') || is_front_page()) {
        wp_enqueue_style('contact-box-style', get_template_directory_uri() . '/assets/css/contact-box.css', array(), theme_asset_version('/assets/css/contact-box.css'));
    }

    // Dashicons - 仅在需要时加载
    if (is_admin_bar_showing() || is_customize_preview()) {
        wp_enqueue_style('dashicons');
    }
    
    // 优化JavaScript加载 - 异步加载，减少阻塞
    // 仅在需要时加载jQuery
    if (is_page_template('templates/contact.php') || is_front_page()) {
        wp_enqueue_script('jquery');
    }

    // 暂时禁用联系方式小方框脚本 - 避免加载问题
    // wp_enqueue_script('contact-box-script', get_template_directory_uri() . '/assets/js/contact-box.js', array(), '1.0.0', true);

    // 为联系表单添加AJAX支持（统一对象名）
    // 本地化数据在下方统一到 ajax_object

    // 注册和加载联系页面脚本 - 仅在联系页面加载
    if (is_page_template('templates/contact.php')) {
        // 仅联系页面加载其专用样式
        wp_enqueue_style('contact-page-style', get_template_directory_uri() . '/assets/css/contact-page.css', array(), theme_asset_version('/assets/css/contact-page.css'));
        // 依赖 jQuery，避免异步导致的竞态
        wp_enqueue_script('contact-page-script', get_template_directory_uri() . '/assets/js/contact-page.js', array('jquery'), theme_asset_version('/assets/js/contact-page.js'), true);
    }

    // 暂时禁用卡片内容平衡器脚本 - 避免加载问题
    // if (get_query_var('industry')) {
    //     wp_enqueue_script('card-content-balancer', get_template_directory_uri() . '/assets/js/card-content-balancer.js', array(), theme_asset_version('/assets/js/card-content-balancer.js'), true);
    //     wp_script_add_data('card-content-balancer', 'defer', true);
    // }

    // {{ AURA-X: Modify - 优化脚本加载优先级和性能. Source: context7-mcp on 'WP Rocket Performance' }}
    // 关键脚本 - 最高优先级，异步加载
    wp_enqueue_script('lazy-loading', get_template_directory_uri() . '/assets/js/lazy-loading.js', array(), theme_asset_version('/assets/js/lazy-loading.js'), false);
    wp_script_add_data('lazy-loading', 'async', true);

    // 现代化核心模块 - 异步加载，避免阻塞
    wp_enqueue_script('modern-core', get_template_directory_uri() . '/assets/js/modern-core.js', array(), theme_asset_version('/assets/js/modern-core.js'), false);
    wp_script_add_data('modern-core', 'async', true);

    // 优化后的主题数据传递
    wp_localize_script('modern-core', 'themeData', array(
        'base' => get_template_directory_uri(),
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('theme_ajax_nonce'),
        'isHome' => is_front_page(),
        'isMobile' => wp_is_mobile(),
        'performance' => array(
            'lazyLoad' => true,
            'webpSupport' => function_exists('imagewebp'),
            'criticalCss' => true
        )
    ));

    // 暂时禁用modern-animations - 避免加载问题
    // wp_enqueue_script('modern-animations', get_template_directory_uri() . '/assets/js/modern-animations.js', array('modern-core'), theme_asset_version('/assets/js/modern-animations.js'), true);
    // wp_script_add_data('modern-animations', 'defer', true);

    // 注册导航脚本（通过队列管理，避免在 head 中直连）
    wp_enqueue_script('navbar', get_template_directory_uri() . '/assets/js/navbar.js', array('modern-core'), theme_asset_version('/assets/js/navbar.js'), true);
    wp_script_add_data('navbar', 'defer', true);

    // 添加关键CSS内联
    add_action('wp_head', 'inline_critical_css', 1);
    
    // 暂时禁用contact-box-script本地化 - 避免加载问题
    // wp_localize_script('contact-box-script', 'ajax_object', array(
    //     'ajax_url' => admin_url('admin-ajax.php'),
    //     'nonce' => wp_create_nonce('contact_form_ajax_nonce')
    // ));
    
    // 为联系页面脚本添加相同的本地化数据
    if (is_page_template('templates/contact.php')) {
        wp_localize_script('contact-page-script', 'ajax_object', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('contact_form_ajax_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'my_theme_scripts');

// 添加主题支持
function my_theme_setup() {
    // 添加标题标签支持
    add_theme_support('title-tag');

    // 添加特色图像支持
    add_theme_support('post-thumbnails');

    // 添加HTML5支持
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // {{ AURA-X: Modify - 英文化导航菜单. Confirmed via 寸止 }}
    // Register navigation menus
    register_nav_menus(array(
        'primary' => 'Primary Navigation',
        'footer' => 'Footer Navigation',
    ));
}
add_action('after_setup_theme', 'my_theme_setup');

// 使用 rewrite + template_include 处理行业详情页
function my_register_industry_rewrite() {
    add_rewrite_tag('%industry%', '([^&]+)');
    add_rewrite_rule('^industry/([^/]+)/?$', 'index.php?industry=$matches[1]', 'top');
}
add_action('init', 'my_register_industry_rewrite');

function my_industry_template_loader($template) {
    $industry = get_query_var('industry');
    if ($industry) {
        // 映射到现有静态模板（保持原有结构）
        $map = array(
            'aluminum' => 'pages/industry-aluminum.php',
            'automotive' => 'pages/industry-automotive.php',
            'ceramic-glass' => 'pages/industry-ceramic-glass.php',
            'appliance' => 'pages/industry-appliance.php',
            'construction' => 'pages/industry-construction.php',
            'elevator' => 'pages/industry-elevator.php',
            'energy' => 'pages/industry-energy.php',
            'fitness' => 'pages/industry-fitness.php',
            'food' => 'pages/industry-food.php',
            'agricultural' => 'pages/industry-agricultural.php',
            'machine-tools' => 'pages/industry-machine-tools.php',
            'material-handling' => 'pages/industry-material-handling.php',
            'packaging' => 'pages/industry-packaging.php',
            'manufacturing' => 'pages/industry-manufacturing.php',
            'medical' => 'pages/industry-medical.php',
            'automation' => 'pages/industry-automation.php',
            'textile' => 'pages/industry-textile.php',
            'tobacco' => 'pages/industry-tobacco.php'
        );

        if (isset($map[$industry])) {
            $path = get_template_directory() . '/' . $map[$industry];
            if (file_exists($path)) {
                return $path;
            }
        }

        // 404 回退
        status_header(404);
        $theme_404 = get_template_directory() . '/templates/404.php';
        if (file_exists($theme_404)) {
            return $theme_404;
        }
    }
    return $template;
}
add_filter('template_include', 'my_industry_template_loader');

// 性能优化功能 - 第一阶段优化
function optimize_theme_performance() {
    // 启用输出缓冲
    // 移除全局无控制的输出缓冲，避免与后续压缩/过滤冲突

    // 移除不必要的WordPress功能
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');

    // 禁用emoji脚本
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');

    // 不再注销 jQuery 为“空脚本”，保持默认或由插件接管

    // {{ AURA-X: Add - 新增高级性能优化. Source: context7-mcp on 'WP Rocket Performance' }}
    // 禁用不必要的嵌入功能
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    remove_action('wp_head', 'wp_oembed_add_host_js');
    remove_action('rest_api_init', 'wp_oembed_register_route');
    remove_filter('oembed_dataparse', 'wp_filter_oembed_result', 10);

    // 优化数据库查询
    add_filter('wp_revisions_to_keep', function($num, $post) {
        return 3; // 限制修订版本数量
    }, 10, 2);

    // 禁用XML-RPC
    add_filter('xmlrpc_enabled', '__return_false');

    // 移除不必要的资源提示
    remove_action('wp_head', 'wp_resource_hints', 2);
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head');
}
add_action('init', 'optimize_theme_performance');

// 添加缓存控制头
function add_cache_headers() {
    if (!is_admin()) {
        // 设置缓存头
        header('Cache-Control: public, max-age=3600');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    }
}
// 暂停主题层缓存头设置，避免与服务器/插件策略冲突
// add_action('send_headers', 'add_cache_headers');

// 压缩HTML输出
function compress_html_output($buffer) {
    if (!is_admin()) {
        // 移除HTML注释（保留条件注释）
        $buffer = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $buffer);
        // 移除多余的空白
        $buffer = preg_replace('/\s+/', ' ', $buffer);
        // 移除行首行尾空白
        $buffer = trim($buffer);
    }
    return $buffer;
}

// 启用HTML压缩（暂时关闭，避免破坏 <script>/<style>/<pre> 内容）
// 如需启用，请实现对敏感标签的排除处理
// function start_html_compression() {
//     if (!is_admin()) {
//         ob_start('compress_html_output');
//     }
// }
// add_action('template_redirect', 'start_html_compression');

// 自动为图片添加懒加载属性
function add_lazy_loading_to_images($content) {
    if (!is_admin() && !is_feed()) {
        // 为img标签添加loading="lazy"属性
        $content = preg_replace('/<img(?![^>]*loading=)([^>]+)>/i', '<img loading="lazy"$1>', $content);

        // 为现有图片添加data-src属性（用于懒加载脚本），但保留原始src，保障无JS时也能显示
        $content = preg_replace_callback(
            '/<img([^>]*?)src=["\']([^"\']+)["\']([^>]*?)>/i',
            function($matches) {
                $before = $matches[1];
                $src = $matches[2];
                $after = $matches[3];

                // 如果已经有data-src，跳过
                if (strpos($before . $after, 'data-src') !== false) {
                    return $matches[0];
                }

                // 添加data-src，但不替换原始src
                return '<img' . $before . 'src="' . $src . '" data-src="' . $src . '"' . $after . '>';
            },
            $content
        );
    }
    return $content;
}
add_filter('the_content', 'add_lazy_loading_to_images');

// 为主题模板中的图片添加懒加载
// 为模板输出添加 lazy 属性（暂时关闭，避免与 WP 原生/JS 懒加载冲突）
// function optimize_theme_images() {
//     ob_start(function($buffer) {
//         if (!is_admin()) {
//             $buffer = preg_replace('/<img(?![^>]*loading=)([^>]+)>/i', '<img loading="lazy"$1>', $buffer);
//         }
//         return $buffer;
//     });
// }
// add_action('template_redirect', 'optimize_theme_images', 1);

// {{ AURA-X: Modify - 增强SEO优化功能. Source: context7-mcp on 'Yoast SEO Optimization' }}
function add_seo_optimizations() {
    // 添加结构化数据
    add_action('wp_head', 'add_structured_data');

    // 优化meta标签
    add_action('wp_head', 'add_optimized_meta_tags');

    // 添加Open Graph标签
    add_action('wp_head', 'add_open_graph_tags');

    // {{ AURA-X: Modify - 移除未定义的函数引用. Confirmed via 寸止 }}
    // 注释掉未实现的SEO函数，避免错误
    // add_action('wp_head', 'add_twitter_card_tags');
    // add_action('wp_head', 'add_jsonld_structured_data');
    // add_action('wp_head', 'add_breadcrumb_schema');
    // add_action('wp_head', 'add_resource_hints');
}
add_action('init', 'add_seo_optimizations');

// 添加结构化数据
function add_structured_data() {
    if (is_front_page()) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'description' => 'Professional timing belt manufacturer serving global industries with precision and reliability since 2003.',
            'foundingDate' => '2003',
            'industry' => 'Industrial Manufacturing',
            'address' => array(
                '@type' => 'PostalAddress',
                'addressLocality' => 'Shanghai',
                'addressCountry' => 'China'
            ),
            'contactPoint' => array(
                '@type' => 'ContactPoint',
                'telephone' => '+86 21 5888 XXXX',
                'contactType' => 'Customer Service'
            ),
            'sameAs' => array(
                'https://linkedin.com/company/timing-belts',
                'https://facebook.com/timingbelts'
            )
        );

        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
}

// 优化meta标签
function add_optimized_meta_tags() {
    // 若安装常见 SEO 插件，则跳过，避免重复
    if (defined('WPSEO_VERSION') || defined('RANK_MATH_VERSION') || defined('AIOSEO_VERSION')) {
        return;
    }
    if (is_front_page()) {
        echo '<meta name="description" content="ISO 9001 certified timing belt manufacturer. Custom solutions for automotive, manufacturing, and industrial applications. Save 20-30% with direct factory pricing. 50+ countries served.">' . "\n";
        echo '<meta name="keywords" content="timing belts, industrial belts, automotive belts, precision belts, custom timing belts, belt manufacturer">' . "\n";
        echo '<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">' . "\n";
        echo '<link rel="canonical" href="' . home_url() . '">' . "\n";
    }
}

// 添加Open Graph标签
function add_open_graph_tags() {
    // 若安装常见 SEO 插件，则跳过，避免重复
    if (defined('WPSEO_VERSION') || defined('RANK_MATH_VERSION') || defined('AIOSEO_VERSION')) {
        return;
    }
    if (is_front_page()) {
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:title" content="' . get_bloginfo('name') . ' - Precision Timing Belts">' . "\n";
        echo '<meta property="og:description" content="ISO 9001 certified timing belt manufacturer serving 50+ countries with custom solutions and direct factory pricing.">' . "\n";
        echo '<meta property="og:url" content="' . home_url() . '">' . "\n";
        echo '<meta property="og:site_name" content="' . get_bloginfo('name') . '">' . "\n";
        echo '<meta property="og:locale" content="en_US">' . "\n";

        // Twitter Cards
        echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
        echo '<meta name="twitter:title" content="' . get_bloginfo('name') . ' - Precision Timing Belts">' . "\n";
        echo '<meta name="twitter:description" content="ISO 9001 certified timing belt manufacturer serving 50+ countries.">' . "\n";
    }
}

// 内联关键CSS
function inline_critical_css() {
    if (is_front_page()) {
        echo '<style id="critical-css">';
        echo ':root{--primary-blue:#1A365D;--primary-orange:#FF6A00;--bg-primary:#f4f6fa;--font-family:"SF Pro Display","Segoe UI",Arial,sans-serif;}';
        echo 'body{margin:0;font-family:var(--font-family);color:#1a2a3a;background:var(--bg-primary);padding-top:var(--navbar-height,75px);}';
        echo '.hero-section{background:linear-gradient(135deg,var(--primary-blue) 0%,#2E5C8A 50%,#4A90E2 100%);color:#fff;padding:0 0 50px 0;min-height:480px;display:flex;align-items:center;}';
        echo '.container{width:90%;max-width:1200px;margin:0 auto;padding:0 15px;}';
        echo '.hero-content{display:flex;align-items:center;gap:60px;position:relative;z-index:2;}';
        echo '.hero-text{flex:1;max-width:600px;}';
        echo '.hero-text h1{font-size:3.2rem;font-weight:700;margin-bottom:20px;line-height:1.2;}';
        echo '.btn-primary{background:linear-gradient(135deg,var(--primary-orange) 0%,#FF8533 100%);color:#fff;padding:12px 28px;border-radius:25px;text-decoration:none;font-weight:600;}';
        echo '</style>';

        // 预加载字体（存在性检查，避免 404）
        $font_file = get_template_directory() . '/assets/fonts/sf-pro-display.woff2';
        if (file_exists($font_file)) {
            echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/fonts/sf-pro-display.woff2" as="font" type="font/woff2" crossorigin>';
        }

        // DNS预连接
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
        echo '<link rel="preconnect" href="//fonts.gstatic.com" crossorigin>';
    }
}

// 优化CSS加载 - 暂停激进的异步注入，改为标准队列加载，避免 FOUC 与重复
// 如需延迟，可使用 rel=preload+onload 或媒体查询策略
// function optimize_css_loading() { ... }

// 第二阶段SEO优化 - 高级SEO功能
function advanced_seo_optimizations() {
    // 添加XML站点地图生成
    add_action('init', 'generate_xml_sitemap');

    // 添加面包屑导航
    add_action('wp_head', 'add_breadcrumb_schema');

    // 优化页面标题（使用 pre_get_document_title 替代旧的 wp_title）
    add_filter('pre_get_document_title', 'my_filter_document_title');

    // 添加行业页面的特定SEO
    add_action('wp_head', 'add_industry_page_seo');
}
add_action('init', 'advanced_seo_optimizations');

// 生成XML站点地图
function generate_xml_sitemap() {
    if (isset($_GET['sitemap']) && $_GET['sitemap'] === 'xml') {
        header('Content-Type: application/xml; charset=utf-8');

        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // 首页
        $sitemap .= '<url>' . "\n";
        $sitemap .= '<loc>' . home_url() . '</loc>' . "\n";
        $sitemap .= '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
        $sitemap .= '<changefreq>weekly</changefreq>' . "\n";
        $sitemap .= '<priority>1.0</priority>' . "\n";
        $sitemap .= '</url>' . "\n";

        // 行业页面
        $industries = array(
            'aluminum', 'automotive', 'ceramic-glass', 'appliance', 'construction',
            'elevator', 'energy', 'fitness', 'food', 'agricultural', 'machine-tools',
            'material-handling', 'packaging', 'manufacturing', 'medical', 'automation',
            'textile', 'tobacco'
        );

        foreach ($industries as $industry) {
            $sitemap .= '<url>' . "\n";
            $sitemap .= '<loc>' . home_url('/industry/' . $industry . '/') . '</loc>' . "\n";
            $sitemap .= '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
            $sitemap .= '<changefreq>monthly</changefreq>' . "\n";
            $sitemap .= '<priority>0.8</priority>' . "\n";
            $sitemap .= '</url>' . "\n";
        }

        $sitemap .= '</urlset>';

        echo $sitemap;
        exit;
    }
}

// 添加面包屑结构化数据
function add_breadcrumb_schema() {
    $industry_slug = get_query_var('industry');
    if ($industry_slug) {
        $industry_names = array(
            'aluminum' => 'Aluminum Industry',
            'automotive' => 'Automotive Industry',
            'ceramic-glass' => 'Ceramic & Glass Industry',
            'energy' => 'Energy Industry',
            'food' => 'Food Industry',
            'medical' => 'Medical Industry',
            'manufacturing' => 'Manufacturing Industry'
        );

        $industry_name = isset($industry_names[$industry_slug]) ? $industry_names[$industry_slug] : ucfirst($industry_slug) . ' Industry';

        $breadcrumb_schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => array(
                array(
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => home_url()
                ),
                array(
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => 'Industries',
                    'item' => home_url('/industries/')
                ),
                array(
                    '@type' => 'ListItem',
                    'position' => 3,
                    'name' => $industry_name,
                    'item' => home_url('/industry/' . $industry_slug . '/')
                )
            )
        );

        echo '<script type="application/ld+json">' . json_encode($breadcrumb_schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
}

// 行业页面特定SEO优化
function add_industry_page_seo() {
    $industry_slug = get_query_var('industry');
    if ($industry_slug) {

        // 行业特定的meta描述和关键词
        $industry_seo = array(
            'automotive' => array(
                'title' => 'Automotive Timing Belts - Precision Solutions for Auto Industry',
                'description' => 'Professional automotive timing belts for assembly lines, machining, and tire building. ISO certified solutions for major automotive manufacturers worldwide.',
                'keywords' => 'automotive timing belts, car timing belts, automotive assembly line belts, precision automotive belts'
            ),
            'energy' => array(
                'title' => 'Energy Industry Timing Belts - Wind, Solar & Power Generation',
                'description' => 'Specialized timing belts for wind turbines, solar tracking systems, and power generation equipment. Reliable solutions for renewable energy applications.',
                'keywords' => 'energy timing belts, wind turbine belts, solar tracking belts, power generation belts'
            ),
            'food' => array(
                'title' => 'Food Grade Timing Belts - FDA Compliant Food Processing',
                'description' => 'FDA compliant food grade timing belts for food processing, packaging, and production lines. Washdown resistant and temperature stable solutions.',
                'keywords' => 'food grade timing belts, FDA compliant belts, food processing belts, washdown belts'
            ),
            'medical' => array(
                'title' => 'Medical Device Timing Belts - Biocompatible & Sterilizable',
                'description' => 'Biocompatible timing belts for medical devices, diagnostic equipment, and pharmaceutical manufacturing. Cleanroom compatible solutions.',
                'keywords' => 'medical timing belts, biocompatible belts, medical device belts, pharmaceutical belts'
            ),
            'packaging' => array(
                'title' => 'Packaging Machinery Timing Belts - High-Speed Precision',
                'description' => 'High-speed timing belts for packaging machinery, filling equipment, and automated packaging lines. Food grade and cleanroom options available.',
                'keywords' => 'packaging timing belts, packaging machinery belts, high speed packaging belts'
            )
        );

        if (isset($industry_seo[$industry_slug])) {
            $seo = $industry_seo[$industry_slug];

            // 交由 WP 核心 title-tag 处理页面标题，避免重复输出 <title>
            echo '<meta name="description" content="' . $seo['description'] . '">' . "\n";
            echo '<meta name="keywords" content="' . $seo['keywords'] . '">' . "\n";
            echo '<link rel="canonical" href="' . home_url('/industry/' . $industry_slug . '/') . '">' . "\n";

            // 行业特定的结构化数据
            $industry_schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'WebPage',
                'name' => $seo['title'],
                'description' => $seo['description'],
                'url' => home_url('/industry/' . $industry_slug . '/'),
                'mainEntity' => array(
                    '@type' => 'Product',
                    'name' => ucfirst($industry_slug) . ' Timing Belts',
                    'description' => $seo['description'],
                    'manufacturer' => array(
                        '@type' => 'Organization',
                        'name' => get_bloginfo('name')
                    ),
                    'category' => 'Industrial Timing Belts'
                )
            );

            echo '<script type="application/ld+json">' . json_encode($industry_schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
        }
    }
}

// 优化页面标题
function optimize_page_titles($title, $sep) {
    if (is_front_page()) {
        return get_bloginfo('name') . ' - Precision Timing Belts Manufacturer | ISO 9001 Certified';
    }
    return $title;
}
// 新的标题过滤器
function my_filter_document_title($title) {
    if (is_front_page()) {
        return get_bloginfo('name') . ' - Precision Timing Belts Manufacturer | ISO 9001 Certified';
    }
    return $title;
}

// 生成优化的robots.txt
function generate_robots_txt() {
    if (isset($_GET['robots']) && $_GET['robots'] === 'txt') {
        header('Content-Type: text/plain; charset=utf-8');

        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /wp-admin/\n";
        $robots .= "Disallow: /wp-includes/\n";
        $robots .= "Disallow: /wp-content/plugins/\n";
        $robots .= "Disallow: /wp-content/themes/\n";
        $robots .= "Allow: /wp-content/themes/*/assets/\n";
        $robots .= "\n";
        $robots .= "# Sitemap\n";
        $robots .= "Sitemap: " . home_url('/?sitemap=xml') . "\n";

        echo $robots;
        exit;
    }
}
add_action('init', 'generate_robots_txt');

// {{ AURA-X: Modify - 增强性能监控和Core Web Vitals. Source: context7-mcp on 'WP Rocket Performance' }}
function add_performance_monitoring() {
    if (!is_admin()) {
        // 添加Google Analytics 4 (示例)
        add_action('wp_head', function() {
            echo '<!-- Google Analytics 4 placeholder -->' . "\n";
            echo '<!-- Replace with actual GA4 tracking code -->' . "\n";
        });

        // 添加Core Web Vitals监控脚本
        add_action('wp_footer', function() {
            echo '<script>';
            echo 'if("performance" in window && "PerformanceObserver" in window){';
            echo 'function sendToAnalytics(metric){';
            echo 'console.log(metric.name + ":", metric.value);';
            echo 'if(window.gtag){gtag("event","web_vitals",{name:metric.name,value:Math.round(metric.value),event_category:"Web Vitals"});}';
            echo '}';
            echo 'new PerformanceObserver((list)=>{';
            echo 'list.getEntries().forEach((entry)=>{';
            echo 'sendToAnalytics({name:"LCP",value:entry.startTime});';
            echo '});';
            echo '}).observe({entryTypes:["largest-contentful-paint"]});';
            echo 'new PerformanceObserver((list)=>{';
            echo 'list.getEntries().forEach((entry)=>{';
            echo 'sendToAnalytics({name:"FID",value:entry.processingStart-entry.startTime});';
            echo '});';
            echo '}).observe({entryTypes:["first-input"]});';
            echo '}';
            echo '</script>';
        });
    }
}
add_action('init', 'add_performance_monitoring');

// 第三阶段：PWA支持
function add_pwa_support() {
    // 添加PWA manifest
    add_action('wp_head', 'add_pwa_manifest');

    // 注册Service Worker
    add_action('wp_footer', 'register_service_worker');

    // 添加PWA相关meta标签
    add_action('wp_head', 'add_pwa_meta_tags');
}
add_action('init', 'add_pwa_support');

// 添加PWA manifest
function add_pwa_manifest() {
    echo '<link rel="manifest" href="' . get_template_directory_uri() . '/manifest.json">' . "\n";

    // iOS特定meta标签
    echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
    echo '<meta name="apple-mobile-web-app-status-bar-style" content="default">' . "\n";
    echo '<meta name="apple-mobile-web-app-title" content="Timing Belts">' . "\n";
    $apple_icon = get_template_directory() . '/assets/images/icon-192x192.png';
    if (file_exists($apple_icon)) {
        echo '<link rel="apple-touch-icon" href="' . get_template_directory_uri() . '/assets/images/icon-192x192.png">' . "\n";
    }

    // Windows特定meta标签
    echo '<meta name="msapplication-TileColor" content="#1A365D">' . "\n";
    $ms_tile = get_template_directory() . '/assets/images/icon-144x144.png';
    if (file_exists($ms_tile)) {
        echo '<meta name="msapplication-TileImage" content="' . get_template_directory_uri() . '/assets/images/icon-144x144.png">' . "\n";
    }
}

// 注册Service Worker
function register_service_worker() {
    if (!is_admin()) {
        echo '<script>';
        echo 'if("serviceWorker" in navigator){';
        echo 'window.addEventListener("load",function(){';
        echo 'navigator.serviceWorker.register("' . get_template_directory_uri() . '/assets/js/service-worker.js")';
        echo '.then(function(registration){';
        echo 'console.log("SW registered: ",registration);';
        echo '}).catch(function(registrationError){';
        echo 'console.log("SW registration failed: ",registrationError);';
        echo '});';
        echo '});';
        echo '}';
        echo '</script>';
    }
}

// 添加PWA相关meta标签
function add_pwa_meta_tags() {
    echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";
    echo '<meta name="application-name" content="Timing Belts">' . "\n";
    echo '<meta name="msapplication-starturl" content="/">' . "\n";
    echo '<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, viewport-fit=cover">' . "\n";
}

// 高级缓存策略
function implement_advanced_caching() {
    // 添加缓存控制头
    add_action('send_headers', function() {
        if (!is_admin()) {
            // 建议在服务器/代理层统一设置缓存策略，避免与插件/服务器冲突
            // 如需启用，请在运维层配置或在此处提供开关

            // 安全头
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
        }
    });
}
add_action('init', 'implement_advanced_caching');

// 主题启用时刷新重写规则（避免首次访问 404）
function my_theme_flush_rewrite_on_switch() {
    my_register_industry_rewrite();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'my_theme_flush_rewrite_on_switch');