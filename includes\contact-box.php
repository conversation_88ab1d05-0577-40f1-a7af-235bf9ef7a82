<?php
/**
 * Contact Box Component
 * Displays a contact button in the bottom right corner, opens a contact form when clicked
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Contact box HTML
function contact_box_html() {
    ?>
    <!-- Contact box trigger button -->
    <div class="contact-box-trigger contact-box-fixed" onclick="console.log('Button clicked directly!'); openContactModal();">
        <span>Contact Us</span>
        <i class="dashicons dashicons-arrow-right-alt"></i>
    </div>

    <!-- Contact form modal -->
    <div class="contact-modal">
        <div class="contact-form-container">
            <span class="contact-form-close">&times;</span>
            <h2>Contact Us</h2>
            <form id="contact-form" class="contact-form">
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="name">First Name <span class="required">*</span></label>
                            <input type="text" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="surname">Last Name <span class="required">*</span></label>
                            <input type="text" id="surname" name="surname" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="email">Email Address <span class="required">*</span></label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="country">Country <span class="required">*</span></label>
                            <select id="country" name="country" required>
                                <option value="">Please select</option>
                                <option value="China">China</option>
                                <option value="United States">United States</option>
                                <option value="United Kingdom">United Kingdom</option>
                                <option value="Germany">Germany</option>
                                <option value="France">France</option>
                                <option value="Japan">Japan</option>
                                <option value="South Korea">South Korea</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="company_type">Company Type</label>
                            <select id="company_type" name="company_type">
                                <option value="">Please select</option>
                                <option value="Manufacturer">Manufacturer</option>
                                <option value="Distributor">Distributor</option>
                                <option value="Retailer">Retailer</option>
                                <option value="Service Provider">Service Provider</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="company">Company Name <span class="required">*</span></label>
                    <input type="text" id="company" name="company" required>
                </div>
                
                <div class="form-group">
                    <label for="message">Message <span class="required">*</span></label>
                    <textarea id="message" name="message" required></textarea>
                </div>
                
                <div class="gdpr-checkbox">
                    <input type="checkbox" id="gdpr" name="gdpr" required>
                    <label for="gdpr">I agree to the processing of my personal data via email, SMS, MMS, fax or similar methods and/or postal services or telephone calls with the operator to send commercial communications about products and services.</label>
                </div>
                
                <button type="submit">Submit</button>
                <?php wp_nonce_field('contact_form_nonce', 'contact_form_nonce'); ?>
            </form>
        </div>
    </div>

    <!-- 内联JavaScript确保功能正常 -->
    <script>
    console.log('Contact box script loaded');

    function openContactModal() {
        console.log('openContactModal called');
        const modal = document.querySelector('.contact-modal');
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            console.log('Modal opened');
        } else {
            console.log('Modal not found');
        }
    }

    function closeContactModal() {
        console.log('closeContactModal called');
        const modal = document.querySelector('.contact-modal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('active');
            document.body.style.overflow = '';
            console.log('Modal closed');
        }
    }

    // 等待DOM加载完成后设置事件
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, setting up contact box events');

        // 关闭按钮
        const closeBtn = document.querySelector('.contact-form-close');
        if (closeBtn) {
            closeBtn.onclick = closeContactModal;
            console.log('Close button event set');
        }

        // 点击背景关闭
        const modal = document.querySelector('.contact-modal');
        if (modal) {
            modal.onclick = function(e) {
                if (e.target === modal) {
                    closeContactModal();
                }
            };
            console.log('Modal background event set');
        }

        // ESC键关闭
        document.onkeydown = function(e) {
            if (e.key === 'Escape') {
                closeContactModal();
            }
        };

        // 表单提交处理
        const form = document.querySelector('#contact-form');
        if (form) {
            form.onsubmit = function(e) {
                e.preventDefault();
                console.log('Form submitted');

                // 简单的成功消息
                const successMsg = document.createElement('div');
                successMsg.innerHTML = '<div style="padding: 1rem; background: #d4edda; color: #155724; border-radius: 0.5rem; margin: 1rem 0; text-align: center;">Thank you for your message! We will contact you shortly.</div>';
                form.insertBefore(successMsg, form.firstChild);

                // 3秒后关闭
                setTimeout(function() {
                    closeContactModal();
                    form.reset();
                    successMsg.remove();
                }, 3000);

                return false;
            };
            console.log('Form submit event set');
        }

        console.log('All contact box events set up');
    });
    </script>
    <?php
}

// {{ AURA-X: Modify - 重新启用联系表单显示. Source: context7-mcp on 'WordPress contact form modal' }}
// 联系表单已修复，重新启用显示
add_action('wp_footer', 'contact_box_html');

// Handle form submission via AJAX
function submit_contact_form_callback() {
    // Verify nonce (localized as ajax_object.nonce)
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'contact_form_ajax_nonce')) {
        wp_send_json_error(array('message' => 'Security verification failed. Please refresh the page and try again.'));
    }

    // Support both serialized form_data and direct FormData posts
    $form_data = array();
    if (isset($_POST['form_data'])) {
        parse_str(wp_unslash($_POST['form_data']), $form_data);
    } else {
        $form_data = wp_unslash($_POST);
    }

    // Detect which form submitted (page form vs floating contact box)
    $is_page_form = isset($form_data['industry']) || isset($form_data['page-industry']);

    // Required fields by form type
    $required_fields = $is_page_form
        ? array('name', 'email', 'company', 'industry', 'country', 'message', 'gdpr')
        : array('name', 'surname', 'email', 'country', 'company', 'message', 'gdpr');

    foreach ($required_fields as $field) {
        if (empty($form_data[$field])) {
            wp_send_json_error(array('message' => 'Please fill in all required fields.'));
        }
    }

    if (!is_email($form_data['email'])) {
        wp_send_json_error(array('message' => 'Please enter a valid email address.'));
    }

    // Prepare data to save
    $contact_data = array(
        'name' => sanitize_text_field($form_data['name'] ?? ''),
        'surname' => sanitize_text_field($form_data['surname'] ?? ''),
        'email' => sanitize_email($form_data['email'] ?? ''),
        'phone' => sanitize_text_field($form_data['phone'] ?? ''),
        'country' => sanitize_text_field($form_data['country'] ?? ''),
        'company' => sanitize_text_field($form_data['company'] ?? ''),
        'company_type' => sanitize_text_field($form_data['company_type'] ?? ''),
        'industry' => sanitize_text_field($form_data['industry'] ?? ''),
        'message' => sanitize_textarea_field($form_data['message'] ?? ''),
        'gdpr' => isset($form_data['gdpr']) ? 'yes' : 'no',
        'date' => current_time('mysql'),
        'ip' => $_SERVER['REMOTE_ADDR'],
    );

    // Save contact information as custom post type
    $post_title = trim(($contact_data['name'] . ' ' . $contact_data['surname'])) . ' - ' . $contact_data['company'];
    $post_id = wp_insert_post(array(
        'post_title' => $post_title,
        'post_content' => $contact_data['message'],
        'post_status' => 'private',
        'post_type' => 'contact_submission',
    ));

    if ($post_id) {
        // Save contact information metadata
        foreach ($contact_data as $key => $value) {
            update_post_meta($post_id, '_contact_' . $key, $value);
        }

        // Handle uploaded files (page form supports files[])
        if (!empty($_FILES['files']) && isset($_FILES['files']['name']) && is_array($_FILES['files']['name'])) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
            $uploaded_urls = array();
            foreach ($_FILES['files']['name'] as $index => $name) {
                if (empty($name)) continue;
                $file_array = array(
                    'name' => $_FILES['files']['name'][$index],
                    'type' => $_FILES['files']['type'][$index],
                    'tmp_name' => $_FILES['files']['tmp_name'][$index],
                    'error' => $_FILES['files']['error'][$index],
                    'size' => $_FILES['files']['size'][$index],
                );
                $overrides = array('test_form' => false);
                $movefile = wp_handle_upload($file_array, $overrides);
                if ($movefile && !isset($movefile['error'])) {
                    $uploaded_urls[] = esc_url_raw($movefile['url']);
                }
            }
            if (!empty($uploaded_urls)) {
                update_post_meta($post_id, '_contact_files', $uploaded_urls);
            }
        }

        // Send email notification
        $to = get_option('admin_email');
        $subject = 'New Contact Form Submission - ' . $contact_data['company'];
        $message = "A new contact form submission has been received:\n\n";
        $message .= "Name: " . trim($contact_data['name'] . ' ' . $contact_data['surname']) . "\n";
        $message .= "Email: " . $contact_data['email'] . "\n";
        $message .= "Phone: " . $contact_data['phone'] . "\n";
        $message .= "Country: " . $contact_data['country'] . "\n";
        if ($contact_data['industry']) { $message .= "Industry: " . $contact_data['industry'] . "\n"; }
        $message .= "Company: " . $contact_data['company'] . "\n";
        if ($contact_data['company_type']) { $message .= "Company Type: " . $contact_data['company_type'] . "\n"; }
        $message .= "Message: \n" . $contact_data['message'] . "\n\n";
        $message .= "Submission Time: " . $contact_data['date'] . "\n";
        $message .= "IP Address: " . $contact_data['ip'] . "\n";
        
        $headers = array('Content-Type: text/plain; charset=UTF-8');
        
        wp_mail($to, $subject, $message, $headers);
        
        wp_send_json_success(array('message' => '<div class="success-icon"></div><p>Thank you for your message. We will contact you shortly!</p>'));
    } else {
        wp_send_json_error(array('message' => 'Submission failed. Please try again later.'));
    }
}
add_action('wp_ajax_submit_contact_form', 'submit_contact_form_callback');
add_action('wp_ajax_nopriv_submit_contact_form', 'submit_contact_form_callback');

// Register contact form submission custom post type
function register_contact_submission_post_type() {
    $args = array(
        'public' => false,
        'publicly_queryable' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => false,
        'capability_type' => 'post',
        'has_archive' => false,
        'hierarchical' => false,
        'menu_position' => 30,
        'menu_icon' => 'dashicons-email-alt',
        'supports' => array('title'),
        'labels' => array(
            'name' => 'Contact Submissions',
            'singular_name' => 'Contact Submission',
            'menu_name' => 'Contact Forms',
            'add_new' => 'Add New',
            'add_new_item' => 'Add New Contact Submission',
            'edit_item' => 'Edit Contact Submission',
            'new_item' => 'New Contact Submission',
            'view_item' => 'View Contact Submission',
            'search_items' => 'Search Contact Submissions',
            'not_found' => 'No contact submissions found',
            'not_found_in_trash' => 'No contact submissions found in trash',
        ),
    );
    register_post_type('contact_submission', $args);
}
add_action('init', 'register_contact_submission_post_type');

// Add custom meta boxes for contact submission details
function add_contact_submission_meta_boxes() {
    add_meta_box(
        'contact_submission_details',
        'Contact Form Details',
        'display_contact_submission_meta_box',
        'contact_submission',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_contact_submission_meta_boxes');

// Display contact submission metadata
function display_contact_submission_meta_box($post) {
    $meta_fields = array(
        '_contact_name' => 'First Name',
        '_contact_surname' => 'Last Name',
        '_contact_email' => 'Email',
        '_contact_phone' => 'Phone',
        '_contact_country' => 'Country',
        '_contact_company' => 'Company Name',
        '_contact_company_type' => 'Company Type',
        '_contact_message' => 'Message',
        '_contact_gdpr' => 'GDPR Consent',
        '_contact_date' => 'Submission Date',
        '_contact_ip' => 'IP Address'
    );
    
    echo '<table class="form-table">';
    foreach ($meta_fields as $key => $label) {
        $value = get_post_meta($post->ID, $key, true);
        echo '<tr>';
        echo '<th><label>' . esc_html($label) . '</label></th>';
        if ($key === '_contact_message') {
            echo '<td><textarea rows="5" cols="50" readonly>' . esc_textarea($value) . '</textarea></td>';
        } else {
            echo '<td><input type="text" value="' . esc_attr($value) . '" readonly style="width:100%"></td>';
        }
        echo '</tr>';
    }
    echo '</table>';
} 