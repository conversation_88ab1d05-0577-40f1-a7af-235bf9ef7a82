/**
 * {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
 * Modern Core JavaScript Module
 * Replaces jQuery dependencies, uses native ES6+ syntax
 * Modular design, load on demand
 */

// {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
// Core utility class
class ModernCore {
    constructor() {
        this.modules = new Map();
        this.observers = new Map();
        this.init();
    }

    init() {
        // {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
        // Initialize after DOM loading is complete
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    onDOMReady() {
        this.initModules();
        this.setupObservers();
        this.optimizePerformance();
    }

    // 模块注册系统
    registerModule(name, moduleClass) {
        this.modules.set(name, moduleClass);
    }

    // 初始化所有模块
    initModules() {
        this.modules.forEach((ModuleClass, name) => {
            try {
                new ModuleClass();
                console.log(`✅ Module ${name} initialized`);
            } catch (error) {
                console.error(`❌ Failed to initialize module ${name}:`, error);
            }
        });
    }

    // 设置观察者
    setupObservers() {
        // 性能观察者
        if ('PerformanceObserver' in window) {
            const perfObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'largest-contentful-paint') {
                        console.log('LCP:', entry.startTime);
                    }
                }
            });
            perfObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        }

        // 可见性观察者
        if ('IntersectionObserver' in window) {
            const visibilityObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('in-view');
                        this.triggerCustomEvent('elementVisible', { element: entry.target });
                    }
                });
            }, { threshold: 0.1 });

            // 观察所有带有 data-observe 属性的元素
            document.querySelectorAll('[data-observe]').forEach(el => {
                visibilityObserver.observe(el);
            });
        }
    }

    // {{ AURA-X: Modify - 增强性能优化功能. Source: context7-mcp on 'WP Rocket Performance' }}
    optimizePerformance() {
        console.log('🚀 Modern Core performance optimization started');

        // 预加载关键资源
        this.preloadCriticalResources();

        // 延迟加载非关键脚本
        this.loadNonCriticalScripts();

        // 优化图片加载
        this.optimizeImages();

        // 启动性能监控
        this.monitorPerformance();

        // 记录性能优化完成时间
        if (window.performance && window.performance.mark) {
            performance.mark('modern-core-performance-optimized');
        }
    }

    preloadCriticalResources() {
        // {{ AURA-X: Modify - 智能资源预加载，基于用户行为. Source: context7-mcp on 'WP Rocket Performance' }}
        const base = (window.themeData && window.themeData.base) ? window.themeData.base : '';
        const themeData = window.themeData || {};

        // 基础关键资源
        const criticalResources = [
            { url: base + '/assets/css/style.css', as: 'style', priority: 'high' }
        ];

        // 条件性资源预加载
        if (themeData.isHome) {
            criticalResources.push(
                { url: base + '/assets/js/contact-box.js', as: 'script', priority: 'high' }
            );
        }

        // 预加载字体
        criticalResources.push(
            { url: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap', as: 'style', priority: 'medium' }
        );

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = resource.as;
            link.href = resource.url;
            if (resource.priority === 'high') {
                link.fetchPriority = 'high';
            }
            if (resource.as === 'style') {
                link.crossOrigin = 'anonymous';
            }
            document.head.appendChild(link);
        });
    }

    loadNonCriticalScripts() {
        // 暂时禁用非关键脚本加载 - 避免加载问题
        // setTimeout(() => {
        //     const base = (window.themeData && window.themeData.base) ? window.themeData.base : '';
        //     const scripts = [
        //         base + '/assets/js/analytics.js',
        //         base + '/assets/js/social-sharing.js'
        //     ];

        //     scripts.forEach(src => {
        //         const script = document.createElement('script');
        //         script.src = src;
        //         script.async = true;
        //         script.onerror = () => { /* 忽略不可用资源 */ };
        //         document.head.appendChild(script);
        //     });
        // }, 2000);
    }

    optimizeImages() {
        // {{ AURA-X: Modify - 现代化图片优化. Source: context7-mcp on 'WP Rocket Performance' }}
        document.querySelectorAll('img').forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
            if (!img.hasAttribute('decoding')) {
                img.setAttribute('decoding', 'async');
            }

            // 添加响应式图片支持
            if (!img.hasAttribute('sizes') && img.hasAttribute('srcset')) {
                img.setAttribute('sizes', '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw');
            }
        });
    }

    monitorPerformance() {
        // 性能监控和Core Web Vitals
        if ('performance' in window && 'PerformanceObserver' in window) {
            // 监控LCP (Largest Contentful Paint)
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                const lastEntry = entries[entries.length - 1];
                console.log('LCP:', lastEntry.startTime);

                // 发送到分析服务（可选）
                if (window.gtag) {
                    gtag('event', 'web_vitals', {
                        name: 'LCP',
                        value: Math.round(lastEntry.startTime),
                        event_category: 'Web Vitals'
                    });
                }
            }).observe({ entryTypes: ['largest-contentful-paint'] });

            // 监控FID (First Input Delay)
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                entries.forEach(entry => {
                    console.log('FID:', entry.processingStart - entry.startTime);
                });
            }).observe({ entryTypes: ['first-input'] });

            // 监控CLS (Cumulative Layout Shift)
            new PerformanceObserver((entryList) => {
                let clsValue = 0;
                const entries = entryList.getEntries();
                entries.forEach(entry => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                });
                console.log('CLS:', clsValue);
            }).observe({ entryTypes: ['layout-shift'] });
        }
    }

    // 自定义事件系统
    triggerCustomEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    // 工具方法
    static $(selector) {
        return document.querySelector(selector);
    }

    static $$(selector) {
        return document.querySelectorAll(selector);
    }

    static ready(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 动画工具
    static animate(element, keyframes, options = {}) {
        if ('animate' in element) {
            return element.animate(keyframes, {
                duration: 300,
                easing: 'ease-out',
                ...options
            });
        }
        return null;
    }

    // AJAX工具
    static async fetch(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Fetch error:', error);
            throw error;
        }
    }
}

// 现代化联系表单模块
class ModernContactForm {
    constructor() {
        this.forms = ModernCore.$$('form[data-contact-form]');
        this.init();
    }

    init() {
        this.forms.forEach(form => {
            this.setupForm(form);
        });
    }

    setupForm(form) {
        form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // 实时验证
        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', ModernCore.debounce(() => this.validateField(input), 300));
        });
    }

    async handleSubmit(e) {
        e.preventDefault();
        const form = e.target;
        
        if (!this.validateForm(form)) {
            return;
        }

        this.showLoading(form);
        
        try {
            const formData = new FormData(form);
            const response = await ModernCore.fetch('/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            });

            this.showSuccess(form);
            form.reset();
        } catch (error) {
            this.showError(form, 'Failed to send message. Please try again.');
        } finally {
            this.hideLoading(form);
        }
    }

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';

        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'This field is required';
        } else if (type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        }

        this.updateFieldStatus(field, isValid, message);
        return isValid;
    }

    validateForm(form) {
        const fields = form.querySelectorAll('input[required], textarea[required]');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    updateFieldStatus(field, isValid, message) {
        const wrapper = field.closest('.form-group') || field.parentElement;
        const errorElement = wrapper.querySelector('.error-message') || this.createErrorElement();
        
        if (!wrapper.querySelector('.error-message')) {
            wrapper.appendChild(errorElement);
        }

        if (isValid) {
            field.classList.remove('error');
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        } else {
            field.classList.add('error');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    createErrorElement() {
        const element = document.createElement('div');
        element.className = 'error-message';
        element.style.cssText = 'color: #e74c3c; font-size: 0.875rem; margin-top: 0.25rem;';
        return element;
    }

    showLoading(form) {
        const button = form.querySelector('button[type="submit"]');
        button.disabled = true;
        button.textContent = 'Sending...';
    }

    hideLoading(form) {
        const button = form.querySelector('button[type="submit"]');
        button.disabled = false;
        button.textContent = 'Send Message';
    }

    showSuccess(form) {
        this.showMessage(form, 'Message sent successfully!', 'success');
    }

    showError(form, message) {
        this.showMessage(form, message, 'error');
    }

    showMessage(form, message, type) {
        const messageElement = document.createElement('div');
        messageElement.className = `form-message ${type}`;
        messageElement.textContent = message;
        messageElement.style.cssText = `
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
        `;

        form.insertBefore(messageElement, form.firstChild);

        setTimeout(() => {
            messageElement.remove();
        }, 5000);
    }
}

// 初始化系统
const modernCore = new ModernCore();

// 注册模块
modernCore.registerModule('ContactForm', ModernContactForm);

// 导出全局访问
window.ModernCore = ModernCore;
window.$ = ModernCore.$;
window.$$ = ModernCore.$$;
